import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

export default class PlcMonitorUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(
		public ip: string,
		public token: string,
		...disposes: Disposes
	) {
		super(...disposes);
		this.panel = vscode.window.createWebviewPanel(
			"plcMonitor",
			"PLC监控",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		this.willDispose(this.panel);

		this.render();

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root" data-ip="${this.ip}" data-token="${this.token}"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.href = "#/plc-monitor-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
