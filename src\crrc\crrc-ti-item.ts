import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcTIUi from "./crrc-ti-ui";
import { type CrrcRacksTIsParams, CrrcTIParams } from "@taotech/plcide-ui";
import CrrcRackItem from "./crrc-rack-item";

export default class CrrcTIItem extends VscodeTreeItem {
	declare parent: CrrcRackItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-ti";
	command = {
		title: "Property",
		command: "taotech.crrc.ti.edit",
		arguments: [this as unknown],
	};

	crrcTIUi?: CrrcTIUi;

	constructor(
		public cfg: CrrcTIParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "TI " + cfg.index, parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcTIUi) this.crrcTIUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcTIUi) {
			this.toJSON();
			this.crrcTIUi = new CrrcTIUi(
				this.parent.parent.ip,
				this.parent.parent.token!,
				this.parent.getChildFreeIndexies(this.cfg.index),
				[{ index: this.parent.cfg.index, children: [this.cfg] }],
				(params) => this.edit(params),
				() => (this.crrcTIUi = undefined),
			);
		} else this.crrcTIUi.reveal();
	}

	edit(params: CrrcRacksTIsParams) {
		const cfg = params[0].children[0];
		if (!this.parent.isChildIndexValidForEdit(cfg.index, this.cfg.index)) return;
		this.cfg = cfg;
		this.label = "TI " + this.cfg.index;
		ide.solution.refresh(this);
		this.parent.parent.parent.save();
	}

	remove() {
		this.parent.parent.parent.removeChild(this.parent, this);
	}
}
