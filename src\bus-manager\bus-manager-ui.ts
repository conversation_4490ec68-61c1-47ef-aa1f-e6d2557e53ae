import vscode from "vscode";
import fs from "fs";
import path from "path";
import * as childProcess from "child_process";
import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import {
	BusMessage,
	canBusVersion,
	companyName,
	PdodataType,
	EditPdodataType,
	dataTypeToBitLength,
	PdoPropertyType,
	busNumMax,
} from "./bus-type";
import BusManagerItem, { BusManagerCfg } from "./bus-manager-item";
import PlcItem from "../plc/plc-item";
import CanSlaveManagerItem, { CanSlaveManagerCfg } from "./can-slave-manager-item";
import CanMasterManagerItem from "./can-master-manager-item";
import { canBusConfigFileSave } from "./can-slave-manager-ui";

export enum BUSEDITTYPE {
	BUSEDITTYPE_NULL = "null",
	BUSEDITTYPE_BUS = "bus",
	BUSEDITTYPE_OTHER = "other",
}

let edsAddFrom: boolean = false;

export default class BusManagerUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	parent: PlcItem | CanMasterManagerItem;
	busEditType: BUSEDITTYPE = BUSEDITTYPE.BUSEDITTYPE_BUS;
	tile: string = "总线管理";
	constructor(
		parent: PlcItem | CanMasterManagerItem,
		busEditType: BUSEDITTYPE,
		private onAdd?: (params: BusManagerCfg | CanSlaveManagerCfg) => void,
		...disposes: Disposes
	) {
		super(...disposes);
		this.parent = parent;
		this.busEditType = busEditType;

		optionUpdate(this.busEditType);
		switch (this.busEditType) {
			case BUSEDITTYPE.BUSEDITTYPE_BUS:
				expandedKeys = ["bus"];
				selectedKeys = ["CANbus"];
				this.tile = "总线管理";
				break;

			case BUSEDITTYPE.BUSEDITTYPE_OTHER:
				expandedKeys = ["0-0", "other"];
				selectedKeys = otherOption.children ? [otherOption.children[0].key] : [""];
				this.tile = "添加CAN从站";
				break;
		}

		this.panel = vscode.window.createWebviewPanel(
			"busManagerUI",
			this.tile,
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		void this.panel.webview.postMessage({
			command: "updateTreeData",
			treeData: optionUpdate(this.busEditType),
			type : this.busEditType,
			optionVernoMap: vernoInfo,
			selectedKeys: selectedKeys,
			expandedKeys: expandedKeys,
		});

		this.render();

		this.panel.webview.onDidReceiveMessage(
			async (message: BusMessage) => {
				// 添加设备事件处理
				if (this.busEditType === BUSEDITTYPE.BUSEDITTYPE_OTHER) {
					switch (message.command) {
						case "BusSelected":
							for (let i = 0; i < vernoInfo.length; i++) {
								if (
									vernoInfo[i].key === message.selectedItem[0] &&
									vernoInfo[i].path.length > 0
								) {
									try {
										await this.addSlave(vernoInfo[i].name, vernoInfo[i].path);
									} catch (error) {
										void error;
										this.dispose();
									}
								}
							}
							this.dispose();
							return;
						case "BusCancel":
							this.dispose();
							return;
						case "BusDelete":
							edsDevicedelete(message.selectedItem[0]);
							return;
					}
					return;
				}

				//添加总线事件处理
				switch (message.command) {
					case "BusSelected":
						switch (message.selectedItem[0]) {
							case "CANbus":
								if ((this.parent as PlcItem).busNum > busNumMax) {
									void vscode.window.showWarningMessage(
										"CANBUS 数量已达到最大值！",
									);
									this.dispose();
									break;
								}
								if (this.onAdd) {
									this.onAdd({
										label: (this.parent as PlcItem).busNum > 0 ? 
												`CANBus_${(this.parent as PlcItem).busNum}` : "CANBus",
										type: BusManagerItem.name,
										index: (this.parent as PlcItem).busNum,
										canPort: 0,
										baudrate: 500,
										canType: 0,
										rackNo: 0,
										nodeId: 1,
										canMasterNum: 0,
									} as BusManagerCfg);
									canBusConfigFileSave();
								}
								this.dispose();
								this.parent.children.forEach((child) => {
									if (child instanceof BusManagerItem) {
										child.showModify();
										return;
									}
								});
								break;
							default:
								void vscode.window.showWarningMessage(
									"You chiose a bus type, but it is not supported yet.",
									message.selectedItem[0],
								);
								break;
						}
						this.dispose();
						break;
					case "BusCancel":
						this.dispose();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidChangeViewState(
			() => {
				if (this.panel.visible) this.render();
				optionUpdate(this.busEditType);
				void this.panel.webview.postMessage({
					command: "updateTreeData",
					treeData: optionUpdate(this.busEditType),
					type : this.busEditType,
					optionVernoMap: vernoInfo,
					selectedKeys: selectedKeys,
					expandedKeys: expandedKeys,
				});
			},
			null,
			this.disposables,
		);

		this.willDispose(this.panel, () => (this.onAdd = undefined));
		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/bus-manager-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}

	async addSlave(name: string, path: string, setnodeId: boolean = false, nodeId: number = 0) {
		let nameIndex = 0;
		let slaveNum = 0;

		try {
			edsData = await readAndParseStructuredEDSFile(path);
			if (edsData) {
				slaveDataUpdate();
				OptionPDOTotalUpdate();
			} else {
				console.error("Failed to parse EDS data from file.");
				return;
			}
		} catch (error) {
			console.error("Error:", error);
			return;
		}

		this.parent.children.forEach((child) => {
			if (child instanceof CanSlaveManagerItem) {
				if (child.name.includes(name)) {
					nameIndex++;
				}
				slaveNum++;
			}
		});

		const canSlaveManagerCfg: CanSlaveManagerCfg = {
			label:
				nameIndex === 0
					? `${name}(${name})`
					:  `${name}_${nameIndex}(${name})`,
			index: slaveNum,
			name: name,
			path: path,
			nodeId: "" + slaveNum,
			expertSetting: true,
			syncProducer: true,
			guarding: {
				nodeProtectDisable: nodeguardNotSupprot(),
				heartBeatDisable: false,
				guardingEnable: false,
				heartBeatProducer: false,
				guardTime: "0",
				producerTime: "200",
				lifeFactor: "0",
			},
			emcy: {
				emcyDisable: true,
				emcy: false,
				cobId: "0",
			},
			timestamp: {
				timestampDisable: true,
				timeProducer: false,
				cobId: "0",
				timeConsumer: false,
			},
			check: {
				vendorId: true,
				productId: false,
				revisionNumber: false,
			},
			rxPdo: [...rxPdoData],
			rxPdoBackup: [] as PdodataType[],
			txPdo: [...txPdoData],
			txPdoBackup: [] as PdodataType[],
			sPdo: [...sdoData],
			optionRxPdo: [...optionRxPdo],
			optionTxPdo: [...optionTxPdo],
			optionSdo: [...optionSdo],
		};

		if (setnodeId) {
			canSlaveManagerCfg.nodeId = "" + nodeId;
		}

		if (this.onAdd) {
			this.onAdd(canSlaveManagerCfg);
		}
	}
}

export let edsData: EDSData = {};
export interface EDSData {
	[section: string]: {
		[key: string]: string | number | EDSData;
	};
}

function nodeguardNotSupprot() {
	if (data10xx.some(item => (item.index === "100c" && 
						item.accessType !== undefined && 
						item.accessType.toLocaleLowerCase().includes("rw"))) 
		&& data10xx.some(item => (item.index === "100d" && 
					item.accessType !== undefined && 
					item.accessType.toLocaleLowerCase().includes("rw")))) {
		return false;
	}

	return true;
}

function isNumeric(input: string): boolean {
	// 判断是否是十进制数字或十六进制数字
	return /^(0x)?[sub0-9A-Fa-f]+$/i.test(input);
}

function convertFirstLetterToLowercase(s: string): string {
	if (s.length === 0) {
		return s;
	}
	return s[0].toLowerCase() + s.slice(1);
}

async function readAndParseStructuredEDSFile(filePath: string): Promise<EDSData> {
	try {
		const fileContent = await fs.promises.readFile(filePath, "utf8");
		const lines = fileContent.split("\n");
		let currentSection: string = ""; // 当前处理的段落标识
		const edsData: EDSData = {};

		for (const line of lines) {
			const trimmedLine = line.trim();
			if (trimmedLine.startsWith("[") && trimmedLine.endsWith("]")) {
				// 新的段落开始
				currentSection = convertFirstLetterToLowercase(trimmedLine.slice(1, -1).trim()); // 移除方括号并去空格
				if (isNumeric(currentSection)) {
					currentSection = currentSection.toLowerCase();
				}
				edsData[currentSection] = {};
			} else if (currentSection && trimmedLine.includes("=")) {
				// 键值对处理
				let [key, value] = convertFirstLetterToLowercase(trimmedLine)
					.split("=")
					.map((part) => part.trim());

				if (value.includes("$NODEID")) {
					value = value.replace("$NODEID", "$nodeId");
				}

				if (key && value) {
					// 根据实际类型进行转换，这里仅作示例
					if (isNumeric(key)) {
						key = key.toLowerCase();
					}
					edsData[currentSection][key] = value; //isNaN(Number(value)) ? value : Number(value);
				}
			}
		}
		return edsData;
	} catch (error) {
		console.error("Error reading or parsing EDS file:", error);
		return {} as EDSData;
	}
}

interface ParsedItem {
	index: string;
	parameterName?: string;
	objectType?: string;
	subNumber?: string;
	dataType?: string;
	lowLimit?: string;
	highLimit?: string;
	accessType?: string;
	defaultValue?: string;
	pDOMapping?: string;
	children?: ParsedItem[];
	[property: string]: unknown; // 添加字符串索引签名
}

function processAndOrganizeData(rawData: EDSData, prefix: string): ParsedItem[] {
	const parsedData: { [key: string]: ParsedItem } = {};

	function processSubItem(key: string, item: ParsedItem): void {
		const parentPrefix = key.replace(/sub\d+/, ""); // 获取前缀，例如 "2000"
		if (parentPrefix in parsedData) {
			const parentItem = parsedData[parentPrefix];
			if (!parentItem.children) {
				parentItem.children = [];
			}
			parentItem.children.push(item);
		}
	}

	Object.keys(rawData).forEach((key) => {
		if (key.startsWith(prefix)) {
			if (!parsedData[key]) {
				parsedData[key] = {
					index: key,
					children: [] as ParsedItem[], // 显式地断言 children 的类型
				};
			}
			Object.keys(rawData[key]).forEach((property) => {
				parsedData[key][property] = rawData[key][property];
			});
		}
	});

	Object.keys(parsedData).forEach((key) => {
		const item = parsedData[key];
		if (/sub\d+/.test(key)) {
			processSubItem(key, item);
		}
	});

	return Object.values(parsedData);
}

function compareIndexes(a: string, b: string): number {
	const numA = parseInt(a, 16);
	const numB = parseInt(b, 16);

	if (numA < numB) {
		return -1;
	} else if (numA > numB) {
		return 1;
	} else {
		return 0;
	}
}

function customSort(a: ParsedItem, b: ParsedItem): number {
	const isASub = a.index.includes("sub");
	const isBSub = b.index.includes("sub");

	if (isASub && !isBSub) {
		return 1; // b 排在 a 前面
	} else if (!isASub && isBSub) {
		return -1; // a 排在 b 前面
	} else {
		// 如果都包含"sub"或都不包含"sub"，则按照数值大小进行排序
		return compareIndexes(a.index, b.index);
	}
}
let data10xx: ParsedItem[];
let data18xx: ParsedItem[];
let data1axx: ParsedItem[];
let data16xx: ParsedItem[];
let data14xx: ParsedItem[];
let rxPdoData: PdodataType[] = [];
let txPdoData: PdodataType[] = [];
const sdoData: PdodataType[] = [];

const optionRxPdo: EditPdodataType[] = [];
const optionTxPdo: EditPdodataType[] = [];
const optionSdo: EditPdodataType[] = [];
let iRx = 0,
	iTx = 0,
	iSdo = 0;

function stringToNumber(dataType: string | undefined): number {
	if (dataType === undefined) {
		return 0;
	}

	if (dataType.includes("0x") || dataType.includes("0X")) {
		return parseInt(dataType.slice(2), 16);
	}

	return parseInt(dataType, 10);
}

function pdoDataGet(items: ParsedItem[], index: string) {
	for (let i = 0; i < items.length; i++) {
		if (items[i].index === index) {
			return items[i];
		}
	}
	return undefined;
}

const pdoPropertyGet = (index: string): PdoPropertyType => {
	let value;
	let cobId = 0,
		transfertype = "1-240",
		suppressTime = 0,
		eventTime = 0,
		syncTime = 0;

	if (edsData === null) {
		return {} as PdoPropertyType;
	}

	const defaultValue = (edsData[index + "sub1"].defaultValue as string).
							toLocaleLowerCase().replace(/\s+/g, '');

	if (edsData[index + "sub1"] && defaultValue) {
		if (defaultValue.includes("$nodeid")) {
			cobId = stringToNumber(defaultValue.slice("$nodeid+".length, defaultValue.length));
		} else {
			cobId = stringToNumber(defaultValue);
		}
	}

	if (edsData[index + "sub2"] && edsData[index + "sub2"].defaultValue) {
		value = stringToNumber(edsData[index + "sub2"].defaultValue as string);
		switch (value) {
			case 0:
				transfertype = "0";
				break;
			case 254:
				transfertype = "254";
				break;
			case 255:
				transfertype = "255";
				break;
			default:
				transfertype = "1-240";
				break;
		}
	}

	if (edsData[index + "sub3"] && edsData[index + "sub3"].defaultValue) {
		suppressTime = stringToNumber(edsData[index + "sub3"].defaultValue as string);
	}

	if (edsData[index + "sub5"] && edsData[index + "sub5"].defaultValue) {
		eventTime = stringToNumber(edsData[index + "sub5"].defaultValue as string);
	}

	if (edsData[index + "sub6"] && edsData[index + "sub6"].defaultValue) {
		syncTime = stringToNumber(edsData[index + "sub6"].defaultValue as string);
	}

	return {
		cobId: cobId,
		suppressTime: suppressTime,
		syncTime: syncTime,
		eventTime: eventTime,
		transfertype: transfertype,
	} as PdoPropertyType;
};

function subPdoPropertyGet(index:number, subIndex:number, data:string) {
	const  indexStr = index.toString(16);
	const subIndexStr1 = `${indexStr}sub${subIndex.toString(16)}`;
	const subIndexStr2 = `${indexStr}sub${subIndex.toString(16).padStart(2, "0")}`;
	if (edsData[subIndexStr1] !== undefined) {
		return edsData[subIndexStr1][data] === undefined ? 
				"" : edsData[subIndexStr1][data] as string;
	}
	if (edsData[subIndexStr2] !== undefined) {
		return edsData[subIndexStr2][data] === undefined ? 
				"" : edsData[subIndexStr2][data] as string;
	}
	if (edsData[indexStr] !== undefined) {
		return edsData[indexStr][data] === undefined ? 
				"" : edsData[indexStr][data] as string;
	}
	return "";
}
function pDOExeclUpdate(type: string): PdodataType[] {
	let comunications: ParsedItem[];
	let mapping: ParsedItem[];
	let defaultValue;
	if (type === "tx") {
		comunications = data18xx;
		mapping = data1axx;
	} else if (type === "rx") {
		comunications = data14xx;
		mapping = data16xx;
	} else {
		return [] as PdodataType[];
	}

	const pdoData: PdodataType[] = [];

	let pdoIndex = 0;
	for (let i = 0; i < mapping.length; i++) {
		if (mapping[i].index.includes("sub")) {
			break;
		}
		const subPdo: PdodataType[] = [];
		let subIndex = 0;
		for (let j = 1; j < mapping[i].children!.length; j++) {
			if (mapping[i].children![j].defaultValue !== undefined) {
				if (stringToNumber(mapping[i].children![j].defaultValue) === 0x80000000) {
					// 未使能 不添加子项
					continue;
				}
				
				const defaultValue = stringToNumber(mapping[i].children![j].defaultValue).toString(
					16,
				);
			
				if (defaultValue.length === 8 && edsData) {
					const pdoIndex = parseInt(defaultValue.slice(0, 4),16);
					const subPdoIndex = parseInt(defaultValue.slice(4, 6),16);
					const dataType =subPdoPropertyGet(pdoIndex, subPdoIndex, "dataType");

					subPdo.push({
						subLength: 0,
						defaultValue: stringToNumber(mapping[i].children![j].defaultValue),
						editProperty: {
							index: parseInt(defaultValue.slice(0, 4), 16),
							subIndex: parseInt(defaultValue.slice(4, 6), 16),
							parameterName:subPdoPropertyGet(pdoIndex, subPdoIndex, "parameterName"),
							objectType: subPdoPropertyGet(pdoIndex, subPdoIndex, "objectType"),
							dataType: dataType,
							lowLimit: subPdoPropertyGet(pdoIndex, subPdoIndex, "lowLimit"),
							highLimit: subPdoPropertyGet(pdoIndex, subPdoIndex, "highLimit"),
							accessType: subPdoPropertyGet(pdoIndex, subPdoIndex, "accessType"),
							pDOMapping: subPdoPropertyGet(pdoIndex, subPdoIndex, "pDOMapping"),
							defaultValue: subPdoPropertyGet(pdoIndex, subPdoIndex, "defaultValue"),
							uniqueKey: 0,
							subLength: 0,
							comment: "",
							pDOMappingBitLength: dataTypeToBitLength(dataType),
							children: [] as EditPdodataType[],
						},
						children: [] as PdodataType[],
					});
					subIndex++;
				}
			}
		}

		const defaultValueStr = comunications[i].children![1].defaultValue?.replace(/\s+/g, "").toLocaleLowerCase();
		if (defaultValueStr!.includes("$nodeid")) {
			defaultValue = stringToNumber(
				defaultValueStr!.slice(
					"$nodeId+".length,
					defaultValueStr!.length,
				),
			);
		} else {
			defaultValue = stringToNumber(comunications[i].children![1].defaultValue);
		}

		let dataType;
		if (pdoDataGet(comunications, comunications[i].index + "sub0") !== undefined) {
			dataType = pdoDataGet(comunications, comunications[i].index + "sub0")!.dataType;
		} else {
			dataType = pdoDataGet(comunications, comunications[i].index)?.dataType;
		}

		pdoData[pdoIndex++] = {
			index: comunications[i].index,
			defaultValue: defaultValue,
			parameterName: comunications[i].parameterName,
			children: subPdo,
			subLength: subIndex,
			property: pdoPropertyGet(comunications[i].index),
			editProperty: {
				dataType: dataType,
				pDOMappingBitLength: dataTypeToBitLength(dataType),
				index: 0,
				subIndex: 0,
				uniqueKey: 0,
				subLength: subIndex,
				comment: "",
				children: [] as EditPdodataType[],
			},
		};
	}

	return pdoData;
}

function slaveDataUpdate() {
	if (edsData) {
		data10xx = processAndOrganizeData(edsData, "10");
		data10xx.sort(customSort);
		data18xx = processAndOrganizeData(edsData, "18");
		data18xx.sort(customSort);
		data1axx = processAndOrganizeData(edsData, "1a");
		data1axx.sort(customSort);
		data16xx = processAndOrganizeData(edsData, "16");
		data16xx.sort(customSort);
		data14xx = processAndOrganizeData(edsData, "14");
		data14xx.sort(customSort);

		rxPdoData = pDOExeclUpdate("rx");
		txPdoData = pDOExeclUpdate("tx");
	}
}

function getData(index: string, param: string) {
	if (edsData === null) {
		return "";
	}

	if (edsData[index] === undefined || edsData[index][param] === undefined) {
		return "";
	}

	return edsData[index][param] ? (edsData[index][param] as string) : "";
}

function OptionPDOUpdate(type: string) {
	if (edsData === null) {
		return;
	}

	const optionalObjects = edsData[type];
	if (optionalObjects) {
		// 遍历OptionalObjects的所有键
		Object.keys(optionalObjects).forEach((key) => {
			if (edsData === null) {
				return;
			}
			const value = stringToNumber(optionalObjects[key] as string);
			let subItem: EditPdodataType[] = [];
			let accessType: string = "";
			let length = 0;
			// 确保key可以转换为数字并且value是一个数字
			const index = parseInt(key, 10);
			if (!isNaN(index)) {
				length = 0;
				if (edsData[value.toString(16)]?.subNumber !== undefined) {
					accessType = "";
					const subNumber = stringToNumber(
						edsData[value.toString(16)]["subNumber"] as string,
					);
					subItem = [];

					let i = 0, j = 0, k = subNumber;
					while (k) {
						const subIndex = value.toString(16) + "sub" + i.toString(16);
						if (edsData[subIndex] !== undefined) {
							k = k - 1;
							let accessType = getData(subIndex, "accessType");
							if (accessType.toLowerCase().includes("rw")) {
								accessType = "rw";
							} else if (accessType.toLowerCase().includes("wo")) {
								accessType = "wo";
							}  else if (accessType.toLowerCase().includes("Const")) {
								accessType = "const";
							}else {
								accessType = "ro";
							}

							subItem[j++] = {
								index: i,
								subIndex: 0,
								subLength: 0,
								parameterName: getData(subIndex, "parameterName"),
								objectType: getData(subIndex, "objectType"),
								dataType: getData(subIndex, "dataType"),
								lowLimit: getData(subIndex, "lowLimit"),
								highLimit: getData(subIndex, "highLimit"),
								accessType: accessType,
								defaultValue: getData(subIndex, "defaultValue"),
								pDOMapping: getData(subIndex, "pDOMapping"),
								pDOMappingBitLength: dataTypeToBitLength(getData(subIndex, "dataType")),
								length: 0,
								uniqueKey: 0,
								children: [] as EditPdodataType[],
							};
						}

						//有些eds标记的个数与实际个数不一致，这里做个限制，预计不会超过两位
						if (i > 0xFF) {
							break;
						}

						i++;
					}
					
					length = subItem.length;
				} else {
					accessType = getData(value.toString(16), "accessType");
				}

				if (accessType === "") {
					accessType = subItem[0]?.accessType === undefined ? "" : subItem[0]?.accessType;
				}

				if (accessType.toLowerCase().includes("rw")) {
					if (
						!(
							(value >= 0x1800 && value <= 0x18ff) ||
							(value >= 0x1a00 && value <= 0x1aff) ||
							(value >= 0x1600 && value <= 0x16ff) ||
							(value >= 0x1400 && value <= 0x14ff)
						)
					) {
						optionRxPdo[iRx++] = {
							index: value,
							subIndex: 0,
							parameterName: getData(value.toString(16), "parameterName"),
							objectType: getData(value.toString(16), "objectType"),
							dataType: getData(value.toString(16), "dataType"),
							lowLimit: getData(value.toString(16), "lowLimit"),
							highLimit: getData(value.toString(16), "highLimit"),
							accessType: "rw",
							defaultValue: getData(value.toString(16), "defaultValue"),
							pDOMapping: getData(value.toString(16), "pDOMapping"),
							pDOMappingBitLength: dataTypeToBitLength(
								getData(value.toString(16), "dataType"),
							),
							uniqueKey: 0,
							children: subItem,
							subLength: length,
						};
					}

					optionSdo[iSdo++] = {
						index: value,
						subIndex: 0,
						parameterName: getData(value.toString(16), "parameterName"),
						objectType: getData(value.toString(16), "objectType"),
						dataType: getData(value.toString(16), "dataType"),
						lowLimit: getData(value.toString(16), "lowLimit"),
						highLimit: getData(value.toString(16), "highLimit"),
						accessType: "rw",
						defaultValue: getData(value.toString(16), "defaultValue"),
						pDOMapping: getData(value.toString(16), "pDOMapping"),
						children: subItem,
						uniqueKey: 0,
						subLength: length,
						pDOMappingBitLength: dataTypeToBitLength(
							getData(value.toString(16), "dataType"),
						),
					};
				} else {
					accessType = "ro";
					if (
						!(
							(value >= 0x1800 && value <= 0x18ff) ||
							(value >= 0x1a00 && value <= 0x1aff) ||
							(value >= 0x1600 && value <= 0x16ff) ||
							(value >= 0x1400 && value <= 0x14ff)
						)
					) {
						optionRxPdo[iRx++] = {
							index: value,
							subIndex: 0,
							parameterName: getData(value.toString(16), "parameterName"),
							objectType: getData(value.toString(16), "objectType"),
							dataType: getData(value.toString(16), "dataType"),
							lowLimit: getData(value.toString(16), "lowLimit"),
							highLimit: getData(value.toString(16), "highLimit"),
							accessType: accessType,
							defaultValue: getData(value.toString(16), "defaultValue"),
							pDOMapping: getData(value.toString(16), "pDOMapping"),
							children: subItem,
							uniqueKey: 0,
							subLength: length,
							pDOMappingBitLength: dataTypeToBitLength(
								getData(value.toString(16), "dataType"),
							),
						};
						optionTxPdo[iTx++] = {
							index: value,
							subIndex: 0,
							parameterName: getData(value.toString(16), "parameterName"),
							objectType: getData(value.toString(16), "objectType"),
							dataType: getData(value.toString(16), "dataType"),
							lowLimit: getData(value.toString(16), "lowLimit"),
							highLimit: getData(value.toString(16), "highLimit"),
							accessType: accessType,
							defaultValue: getData(value.toString(16), "defaultValue"),
							pDOMapping: getData(value.toString(16), "pDOMapping"),
							children: subItem,
							subLength: length,
							uniqueKey: 0,
							pDOMappingBitLength: dataTypeToBitLength(
								getData(value.toString(16), "dataType"),
							),
						};
					}
					optionSdo[iSdo++] = {
						index: value,
						subIndex: 0,
						parameterName: getData(value.toString(16), "parameterName"),
						objectType: getData(value.toString(16), "objectType"),
						dataType: getData(value.toString(16), "dataType"),
						lowLimit: getData(value.toString(16), "lowLimit"),
						highLimit: getData(value.toString(16), "highLimit"),
						accessType: accessType,
						defaultValue: getData(value.toString(16), "defaultValue"),
						pDOMapping: getData(value.toString(16), "pDOMapping"),
						children: subItem,
						subLength: length,
						uniqueKey: 0,
						pDOMappingBitLength: dataTypeToBitLength(
							getData(value.toString(16), "dataType"),
						),
					};
				}
			}
		});
	}
}

function OptionPDOTotalUpdate() {
	iRx = 0;
	iTx = 0;
	iSdo = 0;
	OptionPDOUpdate("mandatoryObjects");
	OptionPDOUpdate("manufacturerObjects");
	OptionPDOUpdate("optionalObjects");
}

interface PopTreeNode {
	title: string;
	key: string;
	selectable?: boolean;
	path : string;
	children?: PopTreeNode[];
}

class VerinoInfo {
	key: string;
	name: string;
	supplier: string;
	category: string;
	version: string;
	path: string;
	constructor(
		key: string,
		name: string,
		supplier: string,
		category: string,
		version: string,
		path: string = "",
	) {
		this.key = key;
		this.name = name;
		this.supplier = supplier;
		this.category = category;
		this.version = version;
		this.path = path;
	}
}

let vernoInfo: VerinoInfo[];

export interface DeviceInfo {
	vendorName: string;
	productName: string;
	eDSVersion: string;
	fileVersion: string;
	fileRevision: string;
	productNumber: string;
	revisionNumber: string;
	vendorNumber: string;
	filePath: string;
}

let deviceInfo: DeviceInfo[];

let verNum = 0;
let deviceNum = 0;
let otherOption: PopTreeNode;
let busOption: PopTreeNode;
let expandedKeys = ["bus"];
let selectedKeys = ["CANbus"];

export function AddDeviceInfoGet(
	productNumber: number,
	revisionNumber: number,
	vendorNumber: number,
) {
	for (let i = 0; i < deviceNum; i++) {
		if (
			parseInt(deviceInfo[i].productNumber, 16) === productNumber &&
			parseInt(deviceInfo[i].revisionNumber, 16) === revisionNumber &&
			parseInt(deviceInfo[i].vendorNumber, 16) === vendorNumber
		) {
			return [true, deviceInfo[i]];
		}

		if (
			parseInt(deviceInfo[i].vendorNumber, 16) === vendorNumber &&
			parseInt(deviceInfo[i].revisionNumber, 16) === revisionNumber
		) {
			return [true, deviceInfo[i]];
		}

		if (
			parseInt(deviceInfo[i].productNumber, 16) === productNumber &&
			parseInt(deviceInfo[i].vendorNumber, 16) === vendorNumber
		) {
			return [true, deviceInfo[i]];
		}

		if (parseInt(deviceInfo[i].vendorNumber, 16) === vendorNumber) {
			return [true, deviceInfo[i]];
		}
	}
	return [false, []];
}

function parseEdsFile(fileContent: string, path: string) {
	const info: DeviceInfo = {
		vendorName: "",
		productName: "",
		eDSVersion: "",
		fileVersion: "",
		fileRevision: "",
		productNumber: "",
		revisionNumber: "",
		vendorNumber: "",
		filePath: "",
	};

	const lines = fileContent.split("\n");
	for (let i = 0; i < lines.length; i++) {
		if (lines[i].includes("VendorName=")) {
			info.vendorName = lines[i].split("=")[1].trim();
		} else if (lines[i].includes("ProductName=")) {
			info.productName = lines[i].split("=")[1].trim();
		} else if (lines[i].includes("EDSVersion=")) {
			info.eDSVersion = lines[i].split("=")[1].trim();
		} else if (lines[i].includes("FileVersion=")) {
			info.fileVersion = lines[i].split("=")[1].trim();
		} else if (lines[i].includes("FileRevision=")) {
			info.fileRevision = lines[i].split("=")[1].trim();
		}

		if (lines[i].includes("1018sub1")) {
			do {
				i++;
				if (lines[i].includes("DefaultValue")) {
					info.vendorNumber = lines[i].split("=")[1].trim();
				}
			} while (!lines[i].includes("["));
		}

		if (lines[i].includes("1018sub2")) {
			do {
				i++;
				if (lines[i].includes("DefaultValue")) {
					info.productNumber = lines[i].split("=")[1].trim();
					break;
				}
			} while (!lines[i].includes("["));
		}

		if (lines[i].includes("1018sub3")) {
			do {
				i++;
				if (lines[i].includes("DefaultValue")) {
					info.revisionNumber = lines[i].split("=")[1].trim();
					break;
				}
			} while (!lines[i].includes("["));
		}
	}

	deviceInfo[deviceNum++] = {
		vendorName: info.vendorName,
		productName: info.productName,
		eDSVersion: info.eDSVersion,
		fileVersion: info.fileVersion,
		fileRevision: info.fileRevision,
		productNumber: info.productNumber,
		revisionNumber: info.revisionNumber,
		vendorNumber: info.vendorNumber,
		filePath: path,
	};

	vernoInfo[verNum++] = new VerinoInfo(
		info.vendorNumber + info.productNumber + info.revisionNumber,
		info.productName,
		info.vendorName,
		"CAN Device",
		info.eDSVersion + "." + info.fileVersion + "." + info.fileRevision,
		path,
	);
	otherOption.children!.push({
		title: info.productName,
		key: info.vendorNumber + info.productNumber + info.revisionNumber,
		path: path,
		selectable: true,
	});
}

function traverseEdsFiles(folderPath: string): void {
	const edsFiles = fs.readdirSync(folderPath).filter((file) => /\.eds$/i.test(file));

	edsFiles.forEach((file) => {
		const filePath = path.join(folderPath, file);
		const fileContent = fs.readFileSync(filePath, "utf-8");
		parseEdsFile(fileContent, filePath);
	});
}

export function optionUpdate(type: BUSEDITTYPE) {
	verNum = 0;
	deviceNum = 0;
	vernoInfo = [];
	deviceInfo = [];

	switch (type) {
		case BUSEDITTYPE.BUSEDITTYPE_OTHER:
			otherOption = {
				title: "CAN从站",
				key: "other",
				selectable: false,
				path: "",
				children: [],
			};

			const edsSavePath = ide.pathJoin(ide.context.globalStorageUri.fsPath, "eds");
			traverseEdsFiles(edsSavePath);
			return [otherOption];
		case BUSEDITTYPE.BUSEDITTYPE_BUS:
			busOption = {
				title: "现场总线",
				key: "bus",
				path: "",
				selectable: false,
				children: [
					{
						title: "CANbus",
						key: "CANbus",
						path: "",
						selectable: true,
					},
				],
			};
			vernoInfo[verNum++] = new VerinoInfo(
				"CANbus",
				"CANbus",
				companyName,
				"CAN总线",
				canBusVersion,
			);
			return [busOption];
	}
}

export function addEdsFromNoFileInPath() {
	edsAddFrom = true;
}

export async function checkForEdsFiles() {
	try {
		// 读取目录内容
		const edsSavePath = ide.pathJoin(ide.context.globalStorageUri.fsPath, "eds");
		if (!fs.existsSync(edsSavePath)) {
			console.error("No eds filepath found.");
			return false; // 未找到 .eds 文件
		}
		const files = await vscode.workspace.fs.readDirectory(vscode.Uri.file(edsSavePath));

		// 检查是否存在 .eds 文件
		for (const [name, type] of files) {
			if (name.toLowerCase().endsWith(".eds") && type === vscode.FileType.File) {
				return true; // 找到 .eds 文件
			}
		}
		console.error("No .eds files found.");
		return false; // 未找到 .eds 文件
	} catch (err) {
		console.error("Error reading directory:", err);
		return false; // 读取目录时出错
	}
}

export function edsDeviceAdd() {
	// 检查路径是否存在
	const edsSavePath = ide.pathJoin(ide.context.globalStorageUri.fsPath, "eds");
	if (!fs.existsSync(edsSavePath)) {
		// 如果路径不存在，创建路径
		fs.mkdirSync(edsSavePath, { recursive: true });
	}

	let title: string;
	if (edsAddFrom) {
		title = "没有EDS文件, 请添加";
		edsAddFrom = false;
	} else {
		title = "选择EDS文件";
	}

	void vscode.window
		.showOpenDialog({
			title: title,
			canSelectFolders: false, // 禁止选择文件夹
			canSelectFiles: true, // 允许选择文件
			canSelectMany: false, // 仅允许选择一个文件
			filters: {
				// 限制文件扩展名为 .eds
				"Text files": ["eds"],
			},
		})
		.then((uri) => {
			if (uri && uri[0]) {
				const selectedFilePath = uri[0].fsPath; // 获取用户选择的文件路径
				// 构建拷贝命令
				const copyCommand = `copy "${selectedFilePath}" "${edsSavePath}"`;

				// 执行拷贝命令
				childProcess.exec(copyCommand, (error) => {
					if (error) {
						void vscode.window.showErrorMessage("设备添加出现错误：" + error.message);
					} else {
						void vscode.window.showInformationMessage("设备添加成功！");
					}
				});
			}
		});
}

export function edsDevicedelete(path: string) {
	// 构建删除命令
	const deleteCommand = `del "${path}"`;

	// 执行删除命令
	childProcess.exec(deleteCommand, (error) => {
		if (error) {
			void vscode.window.showErrorMessage("设备删除出现错误：" + error.message);
		}
	});
}