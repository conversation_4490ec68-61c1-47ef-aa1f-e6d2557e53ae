import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcModbusRtuSlaveUi from "./crrc-modbus-rtu-slave-ui";
import {
	type CrrcPlcModbusRtuSlaveParams,
	CrrcModbusRtuSlaveParams,
} from "@taotech/plc-ide-ui";

export default class CrrcModbusRtuSlaveItem extends VscodeTreeItem {
	declare parent: PlcItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-modbus-rtu-slave";
	command = {
		title: "Property",
		command: "taotech.crrc.modbus.rtu.slave.edit",
		arguments: [this as unknown],
	};

	crrcModbusRtuSlaveUi?: CrrcModbusRtuSlaveUi;

	constructor(
		public cfg: CrrcModbusRtuSlaveParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "Modbus RTU Slave", parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcModbusRtuSlaveUi) this.crrcModbusRtuSlaveUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcModbusRtuSlaveUi) {
			this.toJSON();
			this.crrcModbusRtuSlaveUi = new CrrcModbusRtuSlaveUi(
				this.parent.ip,
				this.parent.token!,
				[this.cfg],
				(params) => this.edit(params),
				() => (this.crrcModbusRtuSlaveUi = undefined),
			);
		} else this.crrcModbusRtuSlaveUi.reveal();
	}

	edit(params: CrrcPlcModbusRtuSlaveParams) {
		this.cfg = params[0];
		ide.solution.refresh(this);
		this.parent.parent.save();
	}

	remove() {
		this.parent.parent.removeChild(this.parent, this);
	}
}
