import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcModbusTcpClientUi from "./crrc-modbus-tcp-client-ui";
import {
	type CrrcPlcModbusTcpClientParams,
	CrrcModbusTcpClientParams,
} from "@taotech/plc-ide-ui";

export default class CrrcModbusTcpClientItem extends VscodeTreeItem {
	declare parent: PlcItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-modbus-tcp-client";
	command = {
		title: "Property",
		command: "taotech.crrc.modbus.tcp.client.edit",
		arguments: [this as unknown],
	};

	crrcModbusTcpClientUi?: CrrcModbusTcpClientUi;

	constructor(
		public cfg: CrrcModbusTcpClientParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "Modbus TCP Master", parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcModbusTcpClientUi) this.crrcModbusTcpClientUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcModbusTcpClientUi) {
			this.toJSON();
			this.crrcModbusTcpClientUi = new CrrcModbusTcpClientUi(
				this.parent.ip,
				this.parent.token!,
				[this.cfg],
				(params) => this.edit(params),
				() => (this.crrcModbusTcpClientUi = undefined),
			);
		} else this.crrcModbusTcpClientUi.reveal();
	}

	edit(params: CrrcPlcModbusTcpClientParams) {
		this.cfg = params[0];
		ide.solution.refresh(this);
		this.parent.parent.save();
	}

	remove() {
		this.parent.parent.removeChild(this.parent, this);
	}
}
