import vscode from "vscode";

import Fs from "fs";

import { ide } from "./extension";
import VscodeTree from "./vscode-tree/vscode-tree";
import ProjectItem from "./project/project-item";
import ProjectPropertyUi from "./project/project-property-ui";
import { upsertProjectSrc } from "./project/project-src";
import { ProjectUpsertParams } from "@taotech/plc-ide-ui";

export default class Solution extends VscodeTree<ProjectItem> {
	newProjectUi?: ProjectPropertyUi;

	constructor() {
		super("taotech-view-solution");
		vscode.workspace.onDidChangeWorkspaceFolders((event) => {
			event.added.forEach((folder) => {
				this.addProjectItem(folder);
			});
			event.removed.forEach((folder) => {
				ide.log.info("remove folder:", folder.uri.fsPath);
				while (true) {
					const index = this.children.findIndex((item) => item.id === folder.uri.fsPath);
					if (index >= 0) {
						const splicedItems = this.children.splice(index, 1);
						for (const spliceItem of splicedItems) spliceItem.dispose();
					} else break;
				}
			});
			this.refresh();
		});
	}

	init() {
		vscode.workspace.workspaceFolders?.forEach((folder) => {
			this.addProjectItem(folder);
		});
		if (this.children.length) this.refresh();
	}

	addProjectItem(folder: vscode.WorkspaceFolder) {
		ide.log.info("open folder ", folder.uri.fsPath);
		const projectFilePath = vscode.Uri.joinPath(folder.uri, ide.projectFileName).fsPath;
		if (Fs.existsSync(projectFilePath)) new ProjectItem(folder);
	}

	showNewProject() {
		if (!this.newProjectUi)
			this.newProjectUi = new ProjectPropertyUi(
				(params) => this.newProject(params),
				() => (this.newProjectUi = undefined),
			);
		else this.newProjectUi.reveal();
	}

	newProject(params: ProjectUpsertParams) {
		upsertProjectSrc(params, false);
		vscode.workspace.updateWorkspaceFolders(
			vscode.workspace.workspaceFolders ? vscode.workspace.workspaceFolders.length : 0,
			null,
			{ uri: vscode.Uri.file(params.folder) },
		);
	}
}
