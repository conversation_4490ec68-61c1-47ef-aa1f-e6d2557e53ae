import vscode from "vscode";

import ChildProcess from "child_process";
import Util from "util";
import Fs from "fs";
import Path from "path";
import Process from "process";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import ProjectItem from "../project/project-item";
import { ProjectChildTask } from "@taotech/plc-ide-ui";

export class TaskSrcCfg implements ProjectChildTask {
	label!: string;
	type?: string;
}
const taskSrcCfgKeys = Object.keys(new TaskSrcCfg()) as Array<keyof TaskSrcCfg>;

export default class TaskSrcItem extends VscodeTreeItem implements TaskSrcCfg {
	iconPath = new vscode.ThemeIcon("window");
	contextValue = "tao-tasksrc";

	declare parent: ProjectItem;

	constructor(cfg: TaskSrcCfg, parent: ProjectItem) {
		super(TaskSrcItem.name, cfg.label, parent, true);
		taskSrcCfgKeys.forEach((key) => {
			(this[key] as unknown) = cfg[key]!;
		});
	}

	toJSON(): unknown {
		const cfg: TaskSrcCfg = new TaskSrcCfg();
		taskSrcCfgKeys.forEach((key) => ((cfg[key] as unknown) = this[key]));
		return cfg;
	}

	async remove() {
		await this.parent.removeTaskSrc(this);
	}

	async matlab() {
		let matlabVerDir = ide.context.globalState.get<string>("mablab.root");
		if (!matlabVerDir) {
			matlabVerDir = ide.onWindows() ? "C:/Program Files/MATLAB/" : "/usr/local/MATLAB/";
			let versions: string[] = [];
			try {
				versions = Fs.readdirSync(matlabVerDir);
			} catch {
				// Intentionally left empty
			}

			if (versions.length === 1) {
				matlabVerDir += versions[0];
			} else if (versions.length > 1) {
				const select = await vscode.window.showQuickPick(versions, {
					canPickMany: false,
					placeHolder: "please select MATLAB version:",
				});
				if (!select) return;
				matlabVerDir += select;
			} else {
				const uris = await vscode.window.showOpenDialog({
					canSelectFolders: true,
					canSelectFiles: false,
					canSelectMany: false,
					title: "Select the version directory of MATLAB installation",
				});
				if (!uris || !uris[0]) return;
				matlabVerDir = uris[0].fsPath;
			}
		}
		ide.context.globalState.update("mablab.root", matlabVerDir);

		const slxUris = await vscode.window.showOpenDialog({
			canSelectFolders: false,
			canSelectFiles: true,
			canSelectMany: false,
			filters: {
				Model: ["slx"],
			},
			title: "Select model file to import",
		});
		if (!slxUris || !slxUris[0]) return;
		const slxPath = slxUris[0].fsPath;
		const slxName = Path.basename(slxPath, Path.extname(slxPath));
		const importDir = Path.join(this.parent.folder, slxName);
		if (!Fs.existsSync(importDir)) await Fs.promises.mkdir(importDir);

		const { stdout, stderr } = await vscode.window.withProgress(
			{
				location: vscode.ProgressLocation.Notification,
				title: "Importing model ......",
				cancellable: false,
			},
			async (progress, token) => {
				return Util.promisify(ChildProcess.execFile)(
					ide.pathJoin(ide.appDir, "bin/mlgen"),
					[slxPath],
					{
						cwd: importDir,
						env: ide.onWindows()
							? { PATH: `${matlabVerDir}/extern/bin/win64;${Process.env.PATH}` }
							: {
									LD_LIBRARY_PATH: `${matlabVerDir}/extern/bin/glnxa64` /*+ `:${matlabVerDir}/sys/os/glnxa64`*/,
								},
						//encoding: "binary", TODO: //https://blog.ctftools.com/2022/08/newpost-47/
					},
				);
			},
		);
		if (stderr) {
			ide.logError(stderr);
			return vscode.window.showErrorMessage("Import failed");
		}
		const cmakeList = "CMakeLists.txt";
		const modelDirEnties = await Fs.promises.readdir(importDir, { recursive: true });
		for (const entry of modelDirEnties) {
			if (entry.endsWith(cmakeList)) {
				const subDir = Path.join(slxName, Path.dirname(entry)).replaceAll(Path.sep, "/");
				Fs.appendFileSync(
					Path.join(this.parent.folder, cmakeList),
					`\nadd_subdirectory(${subDir})`,
				);

				const modelCmakeList = Fs.readFileSync(Path.join(importDir, entry), {
					encoding: "utf8",
				});
				const matchs = modelCmakeList.matchAll(/add_library\((\w+) /gm);
				for (const match of matchs) {
					Fs.appendFileSync(
						Path.join(this.parent.folder, this.label, cmakeList),
						`\ntarget_link_libraries(\${PLC_TASK_NAME} ${match[1]})`,
					);
				}

				Fs.appendFileSync(
					Path.join(this.parent.folder, this.label, "lib.h"),
					`\n#include "${slxName}.h"`,
				);
			}
		}
		return vscode.window.showInformationMessage("Import successfully");
	}

	async build() {
		try {
			const cmakeProject = await ide.cmake?.getProject(this.parent.workspaceFolder.uri);
			await cmakeProject?.configure();
			await cmakeProject?.build([this.label]);
		} catch {
			vscode.commands.executeCommand("workbench.action.problems.focus");
		}
	}
}
