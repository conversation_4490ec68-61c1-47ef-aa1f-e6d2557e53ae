import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcADUi from "./crrc-ad-ui";
import { type CrrcRacksADsParams, CrrcADParams } from "@taotech/plc-ide-ui";
import CrrcRackItem from "./crrc-rack-item";

export default class CrrcADItem extends VscodeTreeItem {
	declare parent: CrrcRackItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-ad";
	command = {
		title: "Property",
		command: "taotech.crrc.ad.edit",
		arguments: [this as unknown],
	};

	crrcADUi?: CrrcADUi;

	constructor(
		public cfg: CrrcADParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "AD " + cfg.index, parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcADUi) this.crrcADUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcADUi) {
			this.toJSON();
			this.crrcADUi = new CrrcADUi(
				this.parent.parent.ip,
				this.parent.parent.token!,
				this.parent.getChildFreeIndexies(this.cfg.index),
				[{ index: this.parent.cfg.index, children: [this.cfg] }],
				(params) => this.edit(params),
				() => (this.crrcADUi = undefined),
			);
		} else this.crrcADUi.reveal();
	}

	edit(params: CrrcRacksADsParams) {
		const cfg = params[0].children[0];
		if (!this.parent.isChildIndexValidForEdit(cfg.index, this.cfg.index)) return;
		this.cfg = cfg;
		this.label = "AD " + this.cfg.index;
		ide.solution.refresh(this);
		this.parent.parent.parent.save();
	}

	remove() {
		this.parent.parent.parent.removeChild(this.parent, this);
	}
}
