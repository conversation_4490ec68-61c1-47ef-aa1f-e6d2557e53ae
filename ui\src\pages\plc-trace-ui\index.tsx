import { useEffect, useRef, useCallback } from "react";
import useState from "react-usestateref";

import {
    Tree,
    Button,
    Drawer,
    Form,
    Input,
    TreeSelect,
    ColorPicker,
    Select,
    Switch,
    InputNumber,
    Tooltip,
} from "@arco-design/web-react";
import { IconPlus, IconMinus, IconEdit } from "@arco-design/web-react/icon";
import TraceChart from "./TraceChart";
import type EChartsReact from "echarts-for-react";

import { vscodeWeb } from "@taotech/libweb";
import {
    apiUrl,
    jsonRpcSubscribe,
    parseJson,
    WebSock,
} from "@taotech/libdual";

import type { Publish, NodeUiKeys, TraceNodes, TraceCfg, LoadVariablesRsp } from "@/msg";
import {
    TraceNode,
    GetStorePathReq,
    GetStorePathOk,
    GetStorePathErr,
    SaveTraceCfgNotify,
    StoreVariableNotify,
    LoadVariablesReq,
} from "@/msg";

const KEY_SEP = ".";

function getThemeName(dark: boolean) {
    return dark ? "dark" : "";
}

interface NodeUiProps {
    onClose: () => void;
    onSubmit: (node: TraceNode) => void;
    node?: TraceNode;
}

class XAxisNode extends TraceNode {
    isLeaf = true;
    addUi = undefined;
    editUi: NodeUiKeys = "XAxisUi";
    removable = false;
    chart = {
        xAxis: {
            splitLine: {
                show: true,
                lineStyle: {
                    color: "gray",
                },
            },
        },
    };
    constructor() {
        super("Time axis");
    }
}

class DiagramNode extends TraceNode {
    children = [new YAxisNode(), new VarsNode()];
    checkable = true;
    addUi = undefined;
    editUi: NodeUiKeys = "DiagramUi";
    removable = true;
    selectedBackgroundColor = "#00000000";
    chart = {
        backgroundColor: "#00000000",
    };
}

class YAxisNode extends TraceNode {
    isLeaf = true;
    addUi = undefined;
    editUi: NodeUiKeys = "YAxisUi";
    removable = false;
    chart = {
        yAxis: {
            splitLine: {
                show: true,
                lineStyle: {
                    color: "gray",
                },
            },
        },
    };
    constructor() {
        super("Y axis");
    }
}

class VarsNode extends TraceNode {
    selectable = false;
    addUi: NodeUiKeys = "VarUi";
    editUi = undefined;
    removable = false;
    constructor() {
        super("Variables");
    }
}

class VarNode extends TraceNode {
    checkable = true;
    isLeaf = true;
    addUi = undefined;
    editUi: NodeUiKeys = "VarUi";
    removable = true;
    chart = {
        series: {
            step: false,
            lineStyle: {
                type: "solid",
                color: "blue",
            },
            itemStyle: {
                color: "red",
            },
            symbol: "circle",
            symbolSize: 4,
        },
    };
}

const root = document.getElementById("root")!;
const ip = root.dataset.ip!;
const token = root.dataset.token!;
const task = root.dataset.task;

export default function App() {
    const [load, setLoad] = useState(true);
    function loadChanged(load: boolean) {
        if (load) {
            setLoad(true);
            Object.keys(varsRecords.current).forEach((key) => delete varsRecords.current[key]);
            socket.current = jsonRpcSubscribe(
                apiUrl(ip, "gvl"),
                "List",
                {
                    token: token,
                    task: task,
                    publishInterval: 1000,
                },
                onPublish,
            );
        } else {
            loadVariablesReq();
        }
    }

    function loadVariablesReq() {
        const req = new LoadVariablesReq();
        window.addEventListener("message", (e) => loadVariablesRsp(e, req));
        vscodeWeb.postMessage(req);
    }

    function loadVariablesRsp(e: MessageEvent<LoadVariablesRsp>, req: LoadVariablesReq) {
        if (!e || !e.data || e.data.id !== req.id || !e.data.result) return;
        if (e.data.result.file) {
            setLoad(false);
            socket.current?.close();
            Object.keys(varsRecords.current).forEach((key) => delete varsRecords.current[key]);
        }
        updateVars(e.data.result.publish);
    }

    const [store, setStore, storeRef] = useState("");
    function storeChanged(store: boolean) {
        if (store) {
            getStorePathReq();
        } else {
            setStore("");
        }
    }

    function getStorePathReq() {
        const req = new GetStorePathReq();
        window.addEventListener("message", (e) => getStorePathRsp(e, req), { once: true });
        vscodeWeb.postMessage(req);
    }

    function getStorePathRsp(
        e: MessageEvent<GetStorePathOk | GetStorePathErr>,
        req: GetStorePathReq,
    ) {
        if (!e || !e.data || e.data.id !== req.id || !e.data.result) return;
        setStore(e.data.result);
    }

    function storeVariable(publish: Publish) {
        if (storeRef.current) vscodeWeb.postMessage(new StoreVariableNotify(publish));
    }

    function saveCfg() {
        vscodeWeb.postMessage(
            new SaveTraceCfgNotify({
                store,
                tree: treeData,
                checkedKeys: treeCheckedKeys,
            }),
        );
    }

    function updateTraceKey(nodes: TraceNodes = treeData, prefix: string = "") {
        nodes.forEach((child, index) => {
            child.key = `${prefix ? `${prefix}${KEY_SEP}` : ""}${index}`;
            if (child.children) updateTraceKey(child.children, child.key);
        });
    }

    function addTraceNode(node: TraceNode, nodes: TraceNodes, parent?: TraceNode) {
        nodes.push(node);

        updateTraceKey();

        const expandParent = parent && !treeExpandedKeys.some((key) => key === parent.key);
        const expandNode = node.children && node.children.length;
        if (expandParent || expandNode) {
            const expandedKeys = [...treeExpandedKeys];
            if (expandParent) expandedKeys.push(parent.key);
            if (expandNode) expandedKeys.push(node.key);
            setTreeExpandedKeys(expandedKeys);
        }

        if (node.checkable) setTreeCheckedKeys(treeCheckedKeys.concat(node.key));

        changeTreeData();
    }

    function delTraceNode(indices: string[], nodes: TraceNodes = treeData) {
        const index = Number(indices.shift());
        if (!indices.length) {
            nodes.splice(index, 1);
            updateTraceKey();
            changeTreeData();
        } else delTraceNode(indices, nodes[index].children);
    }

    function onPublish(publish?: Publish) {
        if (!publish) return;
        updateVars(publish);
        storeVariable(publish);
    }

    function updateVars(publish: Publish) {
        publish.forEach((variable) => {
            const varKey = variable.task + KEY_SEP + variable.name;
            let varRecords = varsRecords.current[varKey];
            if (!varRecords) varRecords = [];
            varRecords.push([variable.timestamp, Number(variable.value)]);
            if (varRecords.length > 1000000) varRecords = varRecords.slice(varRecords.length / 2);
            varsRecords.current[varKey] = varRecords;
        });

        chartsRef.current.forEach((chart) => {
            if (!chart) return;
            const oldOption = chart.getEchartsInstance().getOption() as {
                series: { name: string }[];
            };
            const newOption = {
                series: oldOption.series.map((oldSerias) => ({
                    data: varsRecords.current[oldSerias.name],
                })),
                //dataZoom: [{ startValue: 0 }],
            };
            /*const limit = 1024;
            for (const newSeries of newOption.series) {
                if (newSeries.data && newSeries.data.length > limit) {
                    newOption.dataZoom[0].startValue =
                        newSeries.data[newSeries.data.length - limit][0];
                    break;
                }
            }*/
            chart.getEchartsInstance().setOption(newOption);
        });
    }

    const socket = useRef<WebSock>();

    const [treeData, setTreeData] = useState<TraceNodes>([new XAxisNode()]);
    function changeTreeData() {
        setTreeData([...treeData]);
    }

    const [treeExpandedKeys, setTreeExpandedKeys] = useState<string[]>([]);

    const [treeCheckedKeys, setTreeCheckedKeys] = useState<string[]>([]);

    const [nodeUiShow, setNodeUiShow] = useState<JSX.Element>();

    const chartsRef = useRef<Array<EChartsReact | null>>([]);
    const [chartTheme, setChartTheme] = useState(getThemeName(vscodeWeb.colorSchemeIsDark()));

    const varsRecords = useRef<{
        [index: string]: [number, number][];
    }>({});

    const nodeUiMap = {
        XAxisUi,
        DiagramUi,
        YAxisUi,
        VarUi,
    };

    useEffect(() => {
        chartsRef.current.splice(treeData.length);
    }, [treeData]);

    useEffect(() => {
        saveCfg();
    }, [treeData, treeCheckedKeys, store]);

    useEffect(() => {
        vscodeWeb.colorSchemeListen((dark) => setChartTheme(getThemeName(dark)));

        const cfg = parseJson<TraceCfg>(root.dataset.cfg!);
        if (cfg) {
            setTreeData(cfg.tree);
            setTreeCheckedKeys(cfg.checkedKeys);
            setStore(cfg.store);
        }

        socket.current = jsonRpcSubscribe(
            apiUrl(ip, "gvl"),
            "List",
            {
                token: token,
                task: task,
                publishInterval: 1000,
            },
            onPublish,
        );

        return () => {
            socket.current?.close();
        };
    }, []);

    const actionIconStyle: React.CSSProperties = {
        position: "absolute",
        top: 10,
        color: "rgb(var(--link-6))",
    };

    return (
        <div className="container m-8 flex flex-row">
            <div className="basis-1/4">
                <div>
                    <Switch
                        checkedText="Online"
                        uncheckedText="Offline"
                        checked={load}
                        onChange={(value) => loadChanged(value)}
                    />
                    <Switch
                        className="ml-4"
                        disabled={!load}
                        checkedText="Save"
                        uncheckedText="Save"
                        checked={Boolean(store)}
                        onChange={(value) => storeChanged(value)}
                    />
                    <span className="ml-4">{store}</span>
                </div>
                <Tree
                    className="mt-4"
                    treeData={treeData}
                    expandedKeys={treeExpandedKeys}
                    onExpand={(keys, extra) => {
                        setTreeExpandedKeys(keys);
                    }}
                    checkedKeys={treeCheckedKeys}
                    onCheck={(keys, extra) => {
                        setTreeCheckedKeys(keys);
                    }}
                    onSelect={(keys, extra) => { }}
                    renderExtra={(treeNode) => {
                        const actionNode = treeNode.dataRef as TraceNode;
                        return (
                            <span>
                                {actionNode.addUi && (
                                    <Tooltip content="Add" triggerProps={{ mouseEnterDelay: 1000 }}>
                                        <IconPlus
                                            style={{
                                                ...actionIconStyle,
                                                right: 0,
                                            }}
                                            onClick={() => {
                                                const Ui = nodeUiMap[actionNode.addUi!];
                                                const ui = (
                                                    <Ui
                                                        onClose={() => setNodeUiShow(undefined)}
                                                        onSubmit={(newNode: TraceNode) => {
                                                            addTraceNode(
                                                                newNode,
                                                                actionNode.children,
                                                                actionNode,
                                                            );
                                                        }}
                                                    ></Ui>
                                                );
                                                setNodeUiShow(ui);
                                            }}
                                        />
                                    </Tooltip>
                                )}
                                {actionNode.editUi && (
                                    <Tooltip
                                        content="Edit"
                                        triggerProps={{ mouseEnterDelay: 1000 }}
                                    >
                                        <IconEdit
                                            style={{
                                                ...actionIconStyle,
                                                right: Number(Boolean(actionNode.addUi)) * 24,
                                            }}
                                            onClick={() => {
                                                const Ui = nodeUiMap[actionNode.editUi!];
                                                const ui = (
                                                    <Ui
                                                        onClose={() => setNodeUiShow(undefined)}
                                                        onSubmit={(newNode: TraceNode) => {
                                                            changeTreeData();
                                                        }}
                                                        node={actionNode}
                                                    ></Ui>
                                                );
                                                setNodeUiShow(ui);
                                            }}
                                        />
                                    </Tooltip>
                                )}
                                {actionNode.removable && (
                                    <Tooltip
                                        content="Delete"
                                        triggerProps={{ mouseEnterDelay: 1000 }}
                                    >
                                        <IconMinus
                                            style={{
                                                ...actionIconStyle,
                                                right:
                                                    (Number(Boolean(actionNode.addUi)) +
                                                        Number(Boolean(actionNode.editUi))) *
                                                    24,
                                            }}
                                            onClick={() =>
                                                delTraceNode(actionNode.key.split(KEY_SEP))
                                            }
                                        />
                                    </Tooltip>
                                )}
                            </span>
                        );
                    }}
                ></Tree>
                <div className="mt-8 text-center">
                    <Button
                        type="primary"
                        onClick={() => {
                            const ui = (
                                <DiagramUi
                                    onClose={() => setNodeUiShow(undefined)}
                                    onSubmit={(newNode: TraceNode) => {
                                        addTraceNode(newNode, treeData);
                                    }}
                                ></DiagramUi>
                            );
                            setNodeUiShow(ui);
                        }}
                    >
                        Add Diagram
                    </Button>
                </div>
            </div>
            <div className="ml-8 basis-3/4">
                {treeData.map((node, index) => {
                    if (index === 0 || !treeCheckedKeys.some((key) => key === node.key))
                        return null;

                    const option = {
                        animation: false,
                        title: { text: node.title, left: "center" },
                        ...(node as DiagramNode).chart,
                        xAxis: { type: "time", ...(treeData[0] as XAxisNode).chart.xAxis },
                        yAxis: {
                            type: "value",
                            ...(node.children[0] as YAxisNode).chart.yAxis,
                        },
                        series: node.children[1].children
                            .filter((varNode) => treeCheckedKeys.some((key) => key === varNode.key))
                            .map((varNode) => {
                                return {
                                    name: varNode.title,
                                    type: "line",
                                    data: varsRecords.current[varNode.title],
                                    ...(varNode as VarNode).chart.series,
                                };
                            }),
                        legend: {
                            show: true,
                            left: "left",
                        },
                        toolbox: {
                            feature: {
                                dataZoom: {},
                            },
                        },
                        dataZoom: [
                            {
                                type: "inside",
                            },
                            {
                                type: "slider",
                            },
                        ],
                    };

                    return (
                        <TraceChart
                            key={index}
                            ref={(e) => (chartsRef.current[index] = e)}
                            theme={chartTheme}
                            style={{ height: "300px", width: "100%" }}
                            notMerge={true}
                            option={option}
                        />
                    );
                })}
            </div>
            <div>{nodeUiShow}</div>
        </div>
    );

    function XAxisUi({ onClose, onSubmit, node }: NodeUiProps) {
        const xAxisNode = (node as XAxisNode) ?? new XAxisNode();

        const [form] = Form.useForm();

        const [showSplitLine, setShowSplitLine] = useState(xAxisNode.chart.xAxis.splitLine.show);

        return (
            <Drawer
                placement={"left"}
                visible
                width={400}
                title={<span>Time axis settings</span>}
                onOk={() => {
                    form.validate()
                        .then(() => {
                            Object.assign(xAxisNode, form.getFields());
                            onSubmit(xAxisNode);
                            onClose();
                        })
                        .catch(() => { });
                }}
                onCancel={onClose}
            >
                <Form
                    form={form}
                    labelCol={{
                        span: 8,
                    }}
                    wrapperCol={{
                        span: 16,
                    }}
                    requiredSymbol={false}
                >
                    <Form.Item label="Grid">
                        <div className="flex flex-row">
                            <Form.Item
                                field="chart.xAxis.splitLine.show"
                                initialValue={xAxisNode.chart.xAxis.splitLine.show}
                                rules={[{ required: true }]}
                                triggerPropName="checked"
                            >
                                <Switch onChange={(value) => setShowSplitLine(value)} />
                            </Form.Item>
                            <Form.Item
                                label="color"
                                field="chart.xAxis.splitLine.lineStyle.color"
                                initialValue={xAxisNode.chart.xAxis.splitLine.lineStyle.color}
                                rules={[{ required: true }]}
                            >
                                <ColorPicker showPreset disabled={!showSplitLine} />
                            </Form.Item>
                        </div>
                    </Form.Item>
                </Form>
            </Drawer>
        );
    }

    function DiagramUi({ onClose, onSubmit, node }: NodeUiProps) {
        const diagramNode = (node as DiagramNode) ?? new DiagramNode("");

        const [form] = Form.useForm();

        return (
            <Drawer
                placement={"left"}
                visible
                width={400}
                title={<span>Diagram settings</span>}
                onOk={() => {
                    form.validate()
                        .then(() => {
                            Object.assign(diagramNode, form.getFields());
                            onSubmit(diagramNode);
                            onClose();
                        })
                        .catch(() => { });
                }}
                onCancel={onClose}
            >
                <Form
                    form={form}
                    labelCol={{
                        span: 16,
                    }}
                    wrapperCol={{
                        span: 8,
                    }}
                    requiredSymbol={false}
                >
                    <Form.Item
                        label="Name"
                        field="title"
                        initialValue={diagramNode.title}
                        rules={[{ required: true }]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label="Background color"
                        field="chart.backgroundColor"
                        initialValue={diagramNode.chart.backgroundColor}
                        rules={[{ required: true }]}
                    >
                        <ColorPicker showPreset />
                    </Form.Item>
                    <Form.Item
                        label="Background color on selection"
                        field="selectedBackgroundColor"
                        initialValue={diagramNode.selectedBackgroundColor}
                        rules={[{ required: true }]}
                    >
                        <ColorPicker showPreset />
                    </Form.Item>
                </Form>
            </Drawer>
        );
    }

    function YAxisUi({ onClose, onSubmit, node }: NodeUiProps) {
        const yAxisNode = (node as YAxisNode) ?? new YAxisNode();

        const [form] = Form.useForm();

        const [showSplitLine, setShowSplitLine] = useState(yAxisNode.chart.yAxis.splitLine.show);

        return (
            <Drawer
                placement={"left"}
                visible
                width={400}
                title={<span>Y axis settings</span>}
                onOk={() => {
                    form.validate()
                        .then(() => {
                            Object.assign(yAxisNode, form.getFields());
                            onSubmit(yAxisNode);
                            onClose();
                        })
                        .catch(() => { });
                }}
                onCancel={onClose}
            >
                <Form
                    form={form}
                    labelCol={{
                        span: 8,
                    }}
                    wrapperCol={{
                        span: 16,
                    }}
                    requiredSymbol={false}
                >
                    <Form.Item label="Grid">
                        <div className="flex flex-row">
                            <Form.Item
                                field="chart.yAxis.splitLine.show"
                                initialValue={yAxisNode.chart.yAxis.splitLine.show}
                                rules={[{ required: true }]}
                                triggerPropName="checked"
                            >
                                <Switch onChange={(value) => setShowSplitLine(value)} />
                            </Form.Item>
                            <Form.Item
                                label="color"
                                field="chart.yAxis.splitLine.lineStyle.color"
                                initialValue={yAxisNode.chart.yAxis.splitLine.lineStyle.color}
                                rules={[{ required: true }]}
                            >
                                <ColorPicker showPreset disabled={!showSplitLine} />
                            </Form.Item>
                        </div>
                    </Form.Item>
                </Form>
            </Drawer>
        );
    }

    function VarUi({ onClose, onSubmit, node }: NodeUiProps) {
        const varNode = (node as VarNode) ?? new VarNode("");

        const [form] = Form.useForm();

        const varSelectTreeData = Object.keys(varsRecords.current).map((key) => {
            return { key, title: key };
        });

        return (
            <Drawer
                placement={"left"}
                visible
                width={400}
                title={<span>Variable settings</span>}
                onOk={() => {
                    form.validate()
                        .then(() => {
                            Object.assign(varNode, form.getFields());
                            onSubmit(varNode);
                            onClose();
                        })
                        .catch(() => { });
                }}
                onCancel={onClose}
            >
                <Form
                    form={form}
                    labelCol={{
                        span: 8,
                    }}
                    wrapperCol={{
                        span: 16,
                    }}
                    requiredSymbol={false}
                >
                    <Form.Item
                        label="Variable"
                        field="title"
                        initialValue={varNode.title}
                        rules={[{ required: true }]}
                    >
                        {node ? (
                            <Input readOnly />
                        ) : (
                            <TreeSelect treeData={varSelectTreeData}></TreeSelect>
                        )}
                    </Form.Item>
                    <Form.Item
                        label="Line type"
                        field="chart.series.lineStyle.type"
                        initialValue={varNode.chart.series.lineStyle.type}
                        rules={[{ required: true }]}
                    >
                        <Select options={["solid", "dashed", "dotted"]}></Select>
                    </Form.Item>
                    <Form.Item
                        label="Line color"
                        field="chart.series.lineStyle.color"
                        initialValue={varNode.chart.series.lineStyle.color}
                        rules={[{ required: true }]}
                    >
                        <ColorPicker showPreset />
                    </Form.Item>
                    <Form.Item
                        label="Point type"
                        field="chart.series.symbol"
                        initialValue={varNode.chart.series.symbol}
                        rules={[{ required: true }]}
                    >
                        <Select
                            options={[
                                "circle",
                                "emptyCircle",
                                "rect",
                                "roundRect",
                                "triangle",
                                "diamond",
                                "pin",
                                "arrow",
                                "none",
                            ]}
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        label="Point color"
                        field="chart.series.itemStyle.color"
                        initialValue={varNode.chart.series.itemStyle.color}
                        rules={[{ required: true }]}
                    >
                        <ColorPicker showPreset />
                    </Form.Item>
                    <Form.Item
                        label="Point size"
                        field="chart.series.symbolSize"
                        initialValue={varNode.chart.series.symbolSize}
                        rules={[{ required: true }]}
                    >
                        <InputNumber mode="button" min={1} max={10} />
                    </Form.Item>
                    <Form.Item
                        label="Stepped"
                        field="chart.series.step"
                        initialValue={varNode.chart.series.step}
                        rules={[{ required: true }]}
                        triggerPropName="checked"
                    >
                        <Switch />
                    </Form.Item>
                </Form>
            </Drawer>
        );
    }
}
