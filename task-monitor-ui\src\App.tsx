import { useState, useEffect, useRef } from "react";

import { Typography } from "@arco-design/web-react";
const { Title } = Typography;

import EChartsReact from "echarts-for-react";

import { vscodeWeb } from "@taotech/libweb";
import { apiUrl, jsonRpcSubscribe, WebSock } from "@taotech/libdual";
import { type TaskUpsertParams } from "@taotech/task-property-ui";

import "./App.css";

interface Summary extends TaskUpsertParams {
	label: string;
	status: string;
	statistic?: {
		totolCycleCount: number;
		totalCycleTime: number;
		avgCycleTime: number;
		avgCycleJitter: number;
		minCycleTime: number;
		minCycleJitter: number;
		maxCycleTime: number;
		maxCycleJitter: number;
	};
	latestCycles: {
		times: number[];
		jitters: number[];
	};
	cpuUsage?: number[];
}

function getThemeName(dark: boolean) {
	return dark ? "dark" : "";
}

export default function App() {
	function update(publish?: Summary) {
		if (!publish) return;
		setLabel(publish.label);
		setStatus(publish.status);
		if (publish.schedule) setSchedule(publish.schedule);
		if (publish.watchdog) setWatchdog(publish.watchdog);
		if (publish.statistic) setStatistic(publish.statistic);
		if (publish.latestCycles) {
			if (publish.latestCycles.times && publish.latestCycles.times.length) {
				cyclesTime.current = cyclesTime.current.concat(publish.latestCycles.times);
				if (cyclesTime.current.length > CHART_SECONDS)
					cyclesTime.current = cyclesTime.current.slice(
						cyclesTime.current.length - CHART_SECONDS,
					);
			}
			if (publish.latestCycles.jitters && publish.latestCycles.jitters.length) {
				cyclesJitter.current = cyclesJitter.current.concat(publish.latestCycles.jitters);
				if (cyclesJitter.current.length > CHART_SECONDS)
					cyclesJitter.current = cyclesJitter.current.slice(
						cyclesJitter.current.length - CHART_SECONDS,
					);
			}

			cyclesChart.current?.getEchartsInstance().setOption({
				series: [
					{
						data: cyclesTime.current,
					},
					{
						data: cyclesJitter.current,
					},
				],
			});
		}
		if (publish.cpuUsage && publish.cpuUsage.length) {
			cpuUsages.current = cpuUsages.current.concat(publish.cpuUsage);
			if (cpuUsages.current.length > CHART_SECONDS)
				cpuUsages.current = cpuUsages.current.slice(
					cpuUsages.current.length - CHART_SECONDS,
				);
			cpuUsagesChart.current?.getEchartsInstance().setOption({
				series: [
					{
						data: cpuUsages.current,
					},
				],
			});
		}
	}

	const socket = useRef<WebSock>();

	const [label, setLabel] = useState("");
	const [status, setStatus] = useState("");
	const [schedule, setSchedule] = useState<Summary["schedule"]>();
	const [watchdog, setWatchdog] = useState<Summary["watchdog"]>();
	const [statistic, setStatistic] = useState<Summary["statistic"]>();

	const CHART_SECONDS = 60;

	const [chartTheme, setChartTheme] = useState(getThemeName(vscodeWeb.colorSchemeIsDark()));

	const cyclesTime = useRef<number[]>(Array<number>(CHART_SECONDS).fill(0));
	const cyclesJitter = useRef<number[]>(Array<number>(CHART_SECONDS).fill(0));
	const cyclesChart = useRef<EChartsReact>(null);

	const cpuUsages = useRef<number[]>(Array<number>(CHART_SECONDS).fill(0));
	const cpuUsagesChart = useRef<EChartsReact>(null);

	useEffect(() => {
		vscodeWeb.colorSchemeListen((dark) => setChartTheme(getThemeName(dark)));

		const root = document.getElementById("root")!;

		socket.current = jsonRpcSubscribe(
			apiUrl(root.dataset.ip!, "task"),
			"Summary",
			{
				token: root.dataset.token!,
				label: root.dataset.label!,
				sampleInterval: 1000000,
				publishInterval: 1000,
			},
			update,
		);

		return () => {
			socket.current?.close();
		};
	}, []);

	return (
		<div className="container mx-4">
			<Title type="primary" className="text-center">
				{label}
			</Title>
			<div className="ml-8">
				<div className="flex flex-row">
					<div className="basis-1/3">状态: {status}</div>
				</div>
				<div className="flex flex-row">
					<div className="basis-1/3">调度类型: {schedule?.type}</div>
					<div className="basis-1/3">优先级: {schedule?.priority}</div>
					{schedule?.type === "Cyclic" && (
						<div className="basis-1/3">周期（毫秒）: {schedule?.interval}</div>
					)}
				</div>
				<div className="flex flex-row">
					<div className="basis-1/3">已执行周期数: {statistic?.totolCycleCount}</div>
					<div className="basis-1/3">
						已执行累计时间（微秒）: {statistic?.totalCycleTime}
					</div>
				</div>
				<div className="flex flex-row">
					<div className="basis-1/3">平均执行时间（微秒）: {statistic?.avgCycleTime}</div>
					<div className="basis-1/3">
						平均执行抖动（微秒）: {statistic?.avgCycleJitter}
					</div>
				</div>
				<div className="flex flex-row">
					<div className="basis-1/3">最大执行时间（微秒）: {statistic?.maxCycleTime}</div>
					<div className="basis-1/3">
						最大执行抖动（微秒）: {statistic?.maxCycleJitter}
					</div>
				</div>
				<div className="flex flex-row">
					<div className="basis-1/3">最小执行时间（微秒）: {statistic?.minCycleTime}</div>
					<div className="basis-1/3">
						最小执行抖动（微秒）: {statistic?.minCycleJitter}
					</div>
				</div>
				{watchdog?.enable && (
					<div className="flex flex-row">
						<div className="basis-1/3">看门狗</div>
						<div className="basis-1/3">灵敏度: {watchdog?.sensitivity}</div>
						<div className="basis-1/3">时间（微秒）: {watchdog?.time}</div>
					</div>
				)}
			</div>

			<div className="flex flex-row items-baseline">
				<Title heading={4} type="primary" className="basis-2/12">
					任务
				</Title>
			</div>
			<EChartsReact
				ref={cyclesChart}
				className="ml-8"
				theme={chartTheme}
				style={{ height: "160px", width: "100%" }}
				option={{
					animation: false,
					xAxis: {
						name: `${CHART_SECONDS}秒`,
						type: "category",
						boundaryGap: false,
						min: 0,
						max: CHART_SECONDS - 1,
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					yAxis: {
						name: "微秒",
						type: "value",
						axisLine: {
							show: true,
						},
						splitLine: {
							show: false,
						},
					},
					legend: {
						data: ["时间", "抖动"],
					},
					series: [
						{
							name: "时间",
							type: "line",
							showSymbol: false,
						},
						{
							name: "抖动",
							type: "line",
							showSymbol: false,
						},
					],
					grid: {
						left: 64,
						top: 32,
						bottom: 8,
						right: 80,
					},
				}}
			/>

			<div className="flex flex-row items-baseline">
				<Title heading={4} type="primary" className="basis-2/12">
					CPU
				</Title>
			</div>
			<EChartsReact
				ref={cpuUsagesChart}
				className="ml-8"
				theme={chartTheme}
				style={{ height: "160px", width: "100%" }}
				option={{
					animation: false,
					xAxis: {
						name: `${CHART_SECONDS}秒`,
						type: "category",
						boundaryGap: false,
						min: 0,
						max: CHART_SECONDS - 1,
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					yAxis: {
						name: "% 使用率",
						type: "value",
						axisLine: {
							show: true,
						},
						splitLine: {
							show: false,
						},
					},
					series: [
						{
							type: "line",
							showSymbol: false,
						},
					],
					grid: {
						left: 64,
						top: 32,
						bottom: 8,
						right: 80,
					},
				}}
			/>
		</div>
	);
}
