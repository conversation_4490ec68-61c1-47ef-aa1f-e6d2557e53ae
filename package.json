{"type": "commonjs", "name": "plcide", "publisher": "crrc", "taotech": {"showDeviceMenu": true}, "displayName": "CRRC PLC IDE", "description": "PLC IDE from CRRC", "version": "1.1.14", "icon": "media/main/taotechplcide.png", "engines": {"vscode": "^1.93.0"}, "categories": ["Other"], "main": "./dist/extension.js", "workspaces": ["libdual", "libweb", "project-property-ui", "plc-monitor-ui", "plc-variables-ui", "plc-log-ui", "plc-trace-ui", "task-property-ui", "task-monitor-ui", "bus-ui/bus-manager-ui", "bus-ui/can-bus-manager-ui", "bus-ui/can-master-manager-ui", "bus-ui/can-slave-manager-ui", "bus-ui/device-scan-ui", "crrc-pcm-ui", "crrc-rbm-ui", "crrc-ad-ui", "crrc-di-ui", "crrc-do-ui", "crrc-ti-ui", "crrc-can-ui", "crrc-modbus-tcp-client-ui", "crrc-modbus-tcp-server-ui", "crrc-modbus-rtu-master-ui", "crrc-modbus-rtu-slave-ui"], "scripts": {"install": "npm install --include-workspace-root --workspaces --if-present --ignore-scripts", "update": "npm update --save --include-workspace-root --workspaces --ignore-scripts", "lint": "eslint", "buildLib": "npm run build --workspaces", "build": "webpack", "vscode:prepublish": "webpack --mode production --devtool hidden-source-map", "pack": "npx vsce package -o setup/stuff/vsc/taotech.plcide.vsix --allow-missing-repository", "clean": "FOR /d /r . %g IN (dist node_modules) DO @IF EXIST \"%g\" rmdir /S /Q \"%g\""}, "dependencies": {"csv": "^6.3.10", "dayjs": "^1.11.13", "jszip": "^3.10.1", "vscode-cmake-tools": "^1.2.0"}, "devDependencies": {"@arco-plugins/vite-react": "^1.3.3", "@eslint/js": "^9.23.0", "@tsconfig/recommended": "^1.0.8", "@types/eslint__js": "^8.42.3", "@types/mocha": "^10.0.10", "@types/node": "~20.16.15", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/vscode": "~1.93.0", "@types/vscode-webview": "^1.57.5", "@vitejs/plugin-react": "^4.3.4", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "@vscode/vsce": "^3.3.2", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.14.0", "less": "^4.2.1", "postcss": "^8.4.49", "prettier": "^3.5.3", "prettier-plugin-embed": "^0.4.15", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.5.2", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "vite": "^5.4.10", "webpack": "^5.97.1", "webpack-cli": "^5.1.4"}, "activationEvents": ["workspaceContains:taotechplcide.json"], "contributes": {"viewsContainers": {"activitybar": [{"id": "taotech-views-container-solution", "title": "PLC IDE", "icon": "media/view-plc.png"}]}, "views": {"taotech-views-container-solution": [{"id": "taotech-view-solution", "name": "PLC IDE", "icon": "media/view-plc.png", "visibility": "visible", "contextualTitle": "PLC IDE"}]}, "viewsWelcome": [{"view": "taotech-view-solution", "contents": "[New PLC Project](command:taotech.project.new)"}], "commands": [{"command": "taotech.project.new", "title": "New PLC Project", "category": "TaoTech", "icon": "$(project)"}, {"command": "taotech.plc.scan", "title": "Scan PLC", "category": "TaoTech", "icon": "$(refresh)"}, {"command": "taotech.plc.add", "title": "Add PLC", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.plc.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.plc.login", "title": "<PERSON><PERSON>", "category": "TaoTech", "icon": "$(sign-in)"}, {"command": "taotech.plc.monitor", "title": "Monitor", "category": "TaoTech", "icon": "$(dashboard)"}, {"command": "taotech.plc.variables", "title": "Variables", "category": "TaoTech", "icon": "$(symbol-variable)"}, {"command": "taotech.plc.trace", "title": "Trace", "category": "TaoTech", "icon": "$(eye-watch)"}, {"command": "taotech.plc.log", "title": "Log", "category": "TaoTech", "icon": "$(note)"}, {"command": "taotech.plc.terminal", "title": "Terminal", "category": "TaoTech", "icon": "$(terminal)"}, {"command": "taotech.plc.debug", "title": "Debug", "category": "TaoTech", "icon": "$(debug)"}, {"command": "taotech.plc.files", "title": "Files", "category": "TaoTech", "icon": "$(files)"}, {"command": "taotech.tasksrc.new", "title": "New Task", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.tasksrc.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.tasksrc.build", "title": "Build", "category": "TaoTech", "icon": "$(pass)"}, {"command": "taotech.tasksrc.matlab", "title": "Import MATLAB model", "category": "TaoTech", "icon": "$(clone)"}, {"command": "taotech.task.add", "title": "Add Task", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.task.build", "title": "Build", "category": "TaoTech", "icon": "$(pass)"}, {"command": "taotech.task.upload", "title": "Upload", "category": "TaoTech", "icon": "$(cloud-upload)"}, {"command": "taotech.task.start", "title": "Start", "category": "TaoTech", "icon": "$(run)"}, {"command": "taotech.task.stop", "title": "Stop", "category": "TaoTech", "icon": "$(stop)"}, {"command": "taotech.task.debug", "title": "Debug", "category": "TaoTech", "icon": "$(debug)"}, {"command": "taotech.task.monitor", "title": "Monitor", "category": "TaoTech", "icon": "$(dashboard)"}, {"command": "taotech.task.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.device.add", "title": "Add <PERSON>", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.plc.bus.add", "title": "Add <PERSON>", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.bus.manager.add", "title": "Add <PERSON>", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.bus.manager.del", "title": "Delete Device", "category": "TaoTech", "icon": "$(del)"}, {"command": "taotech.bus.manager.edit", "title": "<PERSON>", "category": "TaoTech", "icon": "$(edit)"}, {"command": "taotech.bus.can.master.add", "title": "Add <PERSON>", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.bus.can.master.del", "title": "Delete Device", "category": "TaoTech", "icon": "$(del)"}, {"command": "taotech.bus.can.master.edit", "title": "<PERSON>", "category": "TaoTech", "icon": "$(edit)"}, {"command": "taotech.bus.can.master.scan", "title": "Sacn Device", "category": "TaoTech", "icon": "$(can)"}, {"command": "taotech.bus.can.slave.del", "title": "Delete Device", "category": "TaoTech", "icon": "$(del)"}, {"command": "taotech.bus.can.slave.edit", "title": "<PERSON>", "category": "TaoTech", "icon": "$(edit)"}, {"command": "taotech.crrc.upsert", "title": "Update PLC", "category": "TaoTech", "icon": "$(cloud-upload)"}, {"command": "taotech.crrc.modbus.rtu.master.add", "title": "Add Modbus RTU Master", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.modbus.rtu.master.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.modbus.rtu.slave.add", "title": "Add Modbus RTU Slave", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.modbus.rtu.slave.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.modbus.tcp.client.add", "title": "Add Modbus TCP Master", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.modbus.tcp.client.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.modbus.tcp.server.add", "title": "Add Modbus TCP Slave", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.modbus.tcp.server.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.rack.add", "title": "Add RBM", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.rack.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.ad.add", "title": "Add AD", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.ad.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.di.add", "title": "Add DI", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.di.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.do.add", "title": "Add DO", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.do.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.ti.add", "title": "Add TI", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.ti.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}, {"command": "taotech.crrc.can.add", "title": "Add CAN", "category": "TaoTech", "icon": "$(add)"}, {"command": "taotech.crrc.can.remove", "title": "Remove", "category": "TaoTech", "icon": "$(remove)"}], "menus": {"file/newFile": [{"command": "taotech.project.new"}], "view/item/context": [{"command": "taotech.plc.scan", "when": "view == taotech-view-solution && viewItem == tao-project"}, {"command": "taotech.plc.add", "when": "view == taotech-view-solution && viewItem == tao-project"}, {"command": "taotech.plc.login", "when": "view == taotech-view-solution && viewItem == tao-plc-off || viewItem == tao-plc-on", "group": "inline"}, {"command": "taotech.plc.monitor", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.variables", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.trace", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.log", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.debug", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.terminal", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.files", "when": "view == taotech-view-solution && viewItem == tao-plc-login"}, {"command": "taotech.plc.remove", "when": "view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.tasksrc.new", "when": "view == taotech-view-solution && viewItem == tao-project"}, {"command": "taotech.tasksrc.remove", "when": "view == taotech-view-solution && viewItem == tao-tasksrc"}, {"command": "taotech.tasksrc.matlab", "when": "view == taotech-view-solution && viewItem == tao-tasksrc"}, {"command": "taotech.tasksrc.build", "when": "view == taotech-view-solution && viewItem == tao-tasksrc"}, {"command": "taotech.task.add", "when": "view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.task.build", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.task.upload", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.task.start", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.task.stop", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.task.monitor", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.task.debug", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.task.remove", "when": "view == taotech-view-solution && viewItem =~ /tao-task-.+/"}, {"command": "taotech.device.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-project"}, {"command": "taotech.plc.bus.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.bus.manager.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-bus"}, {"command": "taotech.bus.manager.del", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-bus"}, {"command": "taotech.bus.manager.edit", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-bus"}, {"command": "taotech.bus.can.master.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-canopen-manager"}, {"command": "taotech.bus.can.master.del", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-canopen-manager"}, {"command": "taotech.bus.can.master.edit", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-canopen-manager"}, {"command": "taotech.bus.can.master.scan", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-canopen-manager"}, {"command": "taotech.bus.can.slave.del", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-canopen-slave"}, {"command": "taotech.bus.can.slave.edit", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-canopen-slave"}, {"command": "taotech.crrc.upsert", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.crrc.modbus.rtu.master.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.crrc.modbus.rtu.master.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-modbus-rtu-master"}, {"command": "taotech.crrc.modbus.rtu.slave.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.crrc.modbus.rtu.slave.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-modbus-rtu-slave"}, {"command": "taotech.crrc.modbus.tcp.client.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.crrc.modbus.tcp.client.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-modbus-tcp-client"}, {"command": "taotech.crrc.modbus.tcp.server.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.crrc.modbus.tcp.server.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-modbus-tcp-server"}, {"command": "taotech.crrc.rack.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem =~ /tao-plc-.+/"}, {"command": "taotech.crrc.rack.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-rack"}, {"command": "taotech.crrc.ad.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-rack"}, {"command": "taotech.crrc.ad.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-ad"}, {"command": "taotech.crrc.di.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-rack"}, {"command": "taotech.crrc.di.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-di"}, {"command": "taotech.crrc.do.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-rack"}, {"command": "taotech.crrc.do.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-do"}, {"command": "taotech.crrc.ti.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-rack"}, {"command": "taotech.crrc.ti.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-ti"}, {"command": "taotech.crrc.can.add", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-rack"}, {"command": "taotech.crrc.can.remove", "when": "taotech.showDeviceMenu && view == taotech-view-solution && viewItem == tao-crrc-can"}]}}}