import React, { useState, useRef } from "react";

import {
	Tabs,
	Form,
	Select,
	InputNumber,
	Button,
	Image,
	Notification,
} from "@arco-design/web-react";
const { Option } = Select;
const TabPane = Tabs.TabPane;

import { vscodeWeb } from "@taotech/libweb";
import { parseJson, apiUrl, jsonRpcCall, JsonRpcRequest } from "@taotech/libdual";

import "./App.css";

import { CrrcDOParams, CrrcUpsertDOReq, CrrcRacksDOsParams } from "./msg.ts";

const outputModes = ["电平输出", "频率输出"];

interface GetStatusParam {
	rack: number;
	node: number;
}

interface GetStatusResult {
	type: number;
	version: number;
	status: number;
}

const StatusMap = ["不在线", "在线"];

const root = document.getElementById("root")!;
const ip = root.dataset.ip!;
const token = root.dataset.token!;
const indexies = parseJson<string[]>(root.dataset.indexies);
const params = parseJson<CrrcRacksDOsParams>(root.dataset.params)!;
const productUrl = root.dataset.productUrl!;

export default function App() {
	function submit(values: CrrcDOParams) {
		values.type = params[0].children[0].type;
		const req = new CrrcUpsertDOReq([{ index: params[0].index, children: [values] }], token);

		vscodeWeb.postMessage(req);

		jsonRpcCall<true, CrrcRacksDOsParams>(apiUrl(ip, "crrc"), req)
			.then(() => {
				vscodeWeb.close();
			})
			.catch((reason) => {
				console.error(reason);
				Notification.error({
					content: "设置失败，请确认设备在线后重试",
				});
			});
	}

	const statusTimer = useRef<ReturnType<typeof setInterval>>();
	const [moduleType, setModuleType] = useState("正在获取...");
	const [moduleVersion, setModuleVersion] = useState("正在获取...");
	const [moduleStatus, setModuleStatus] = useState("正在获取...");
	function onTabChange(key: string) {
		if (key === "status") {
			statusTimer.current = setInterval(() => {
				jsonRpcCall<GetStatusResult, GetStatusParam>(
					apiUrl(ip, "crrc"),
					new JsonRpcRequest(
						"getStatus",
						{ rack: params[0].index, node: form.getFieldValue("index") },
						token,
					),
				)
					.then((result: GetStatusResult) => {
						setModuleType(String(result.type));
						setModuleVersion(String(result.version));
						setModuleStatus(StatusMap[result.status]);
					})
					.catch((reason) => {
						setModuleType("获取失败");
						setModuleVersion("获取失败");
						setModuleStatus("获取失败");
						console.error(reason);
					});
			}, 1000);
		} else {
			clearInterval(statusTimer.current);
		}
	}

	const [form] = Form.useForm();

	return (
		<Tabs tabPosition="left" className="m-4" onChange={onTabChange}>
			<TabPane key="config" title="参数配置">
				<div className="flex flex-col gap-y-8">
					<p className="italic">
						提示：刷新周期仅DO为电平输出时有效（单位：us）；输出频率仅DO为频率输出时有效（单位：Hz）
					</p>

					<Form
						form={form}
						initialValues={params[0].children[0]}
						labelCol={{ span: 3 }}
						onSubmit={submit}
					>
						<Form.Item label="节点编号" field="index">
							<Select options={indexies} />
						</Form.Item>
						<Form.Item label="通道">
							<div className="grid grid-cols-3 gap-4">
								<span>输出模式</span>
								<span>刷新周期(us)</span>
								<span>输出频率(Hz)</span>
							</div>
						</Form.Item>
						<Form.Item label="1-4" style={{ marginBottom: 0 }}>
							<div className="grid grid-cols-3 gap-4">
								<Form.Item
									field="channel1to4.mode"
									normalize={(value) => outputModes.indexOf(value)}
									formatter={(value) => outputModes[value]}
								>
									<Select options={outputModes} />
								</Form.Item>
								<Form.Item
									field="channel1to4.cycle"
									rules={[{ type: "number", min: 0 }]}
								>
									<InputNumber />
								</Form.Item>
								<Form.Item
									field="channel1to4.frequency"
									rules={[{ type: "number", min: 1, max: 2000 }]}
								>
									<InputNumber />
								</Form.Item>
							</div>
						</Form.Item>
						<Form.Item label="5-8" style={{ marginBottom: 0 }}>
							<div className="grid grid-cols-3 gap-4">
								<Form.Item
									field="channel5to8.mode"
									normalize={(value) => outputModes.indexOf(value)}
									formatter={(value) => outputModes[value]}
								>
									<Select options={outputModes} />
								</Form.Item>
								<Form.Item
									field="channel5to8.cycle"
									rules={[{ type: "number", min: 0 }]}
								>
									<InputNumber />
								</Form.Item>
								<Form.Item
									field="channel5to8.frequency"
									rules={[{ type: "number", min: 1, max: 2000 }]}
								>
									<InputNumber />
								</Form.Item>
							</div>
						</Form.Item>
						<Form.Item label="9-12" style={{ marginBottom: 0 }}>
							<div className="grid grid-cols-3 gap-4">
								<Form.Item
									field="channel9to12.mode"
									normalize={(value) => outputModes.indexOf(value)}
									formatter={(value) => outputModes[value]}
								>
									<Select options={outputModes} />
								</Form.Item>
								<Form.Item
									field="channel9to12.cycle"
									rules={[{ type: "number", min: 0 }]}
								>
									<InputNumber />
								</Form.Item>
								<Form.Item
									field="channel9to12.frequency"
									rules={[{ type: "number", min: 1, max: 2000 }]}
								>
									<InputNumber />
								</Form.Item>
							</div>
						</Form.Item>
						<Form.Item label="13-16" style={{ marginBottom: 0 }}>
							<div className="grid grid-cols-3 gap-4">
								<Form.Item
									field="channel13to16.mode"
									normalize={(value) => outputModes.indexOf(value)}
									formatter={(value) => outputModes[value]}
								>
									<Select options={outputModes} />
								</Form.Item>
								<Form.Item
									field="channel13to16.cycle"
									rules={[{ type: "number", min: 0 }]}
								>
									<InputNumber />
								</Form.Item>
								<Form.Item
									field="channel13to16.frequency"
									rules={[{ type: "number", min: 1, max: 2000 }]}
								>
									<InputNumber />
								</Form.Item>
							</div>
						</Form.Item>
						<Form.Item wrapperCol={{ offset: 11 }}>
							<Button type="primary" htmlType="submit">
								确定
							</Button>
						</Form.Item>
					</Form>
				</div>
			</TabPane>
			<TabPane key="status" title="状态">
				<div>类型：{moduleType}</div>
				<div>版本号：{moduleVersion}</div>
				<div>状态：{moduleStatus}</div>
			</TabPane>
			<TabPane key="info" title="信息" className="flex gap-8">
				{/* prettier-ignore */}
				<div>
型号：G1-16DO<br/>
<br/>
<u>接口性能</u><br/>
信号类型：Mosfet高边输出，常开触点<br/>
负载电源：外部供电，DC24V Max<br/>
信号路数：×16<br/>
驱动能力：DC24V持续供电电流，0.5A Max<br/>
短路保护：1A，保险慢融<br/>
接通延迟：2ms Max<br/>
关断延迟：2ms Max<br/>
绝缘性能：与外部供电电源隔离，绝缘能力AC750V 1min<br/>
<br/>
<u>环境适应性能</u><br/>
温度：运行温度-40℃~70℃，存储温度-40℃~85℃<br/>
湿度：工作、存储湿度5%～95%<br/>
防护：防护等级IP20<br/>
				</div>
				<Image src={productUrl} />
			</TabPane>
		</Tabs>
	);
}
