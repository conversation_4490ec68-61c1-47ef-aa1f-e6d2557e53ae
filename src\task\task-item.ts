import vscode from "vscode";

import Fs from "fs";
import Path from "path";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import { TaskUpsertParams } from "@taotech/plc-ide-ui";
import TaskPropertyUi from "../task/task-property-ui";
import TaskMonitorUi from "../task/task-monitor-ui";
import type { Task } from "@taotech/plc-ide-ui";
import Zip from "jszip";
import { jsonRpcUpload, JsonRpcRequest, apiUrl, jsonRpcCall, parseJson } from "@taotech/libdual";

type TaskPkgStatus = "match" | "local" | "remote";
type TaskRunStatus = Task["status"];
type TaskStatus = `tao-task-${TaskPkgStatus}-${TaskRunStatus}`;

export class TaskCfg implements TaskUpsertParams {
	type?: string;
	label!: string;
	children?: VscodeTreeItem[];
	schedule!: TaskUpsertParams["schedule"];
	watchdog!: TaskUpsertParams["watchdog"];
}
const taskCfgKeys = Object.keys(new TaskCfg()) as Array<keyof TaskCfg>;

export default class TaskItem extends VscodeTreeItem implements TaskCfg {
	iconPath = new vscode.ThemeIcon("server-process");
	contextValue: TaskStatus = "tao-task-local-Idle";
	command = {
		title: "Property",
		command: "taotech.task.modify",
		arguments: [this as unknown],
	};

	declare parent: PlcItem;

	schedule!: TaskUpsertParams["schedule"];
	watchdog!: TaskUpsertParams["watchdog"];

	taskPropertyUi?: TaskPropertyUi;
	taskMonitorUi?: TaskMonitorUi;

	constructor(cfg: TaskCfg, parent: PlcItem) {
		cfg.type = TaskItem.name;
		super(TaskItem.name, cfg.label, parent, true);
		this.disposer.willDispose(() => {
			if (this.taskPropertyUi) this.taskPropertyUi.dispose();
			if (this.taskMonitorUi) this.taskMonitorUi.dispose();
		});
		taskCfgKeys.forEach((key) => {
			if (key !== "children") (this[key] as unknown) = cfg[key]!;
		});
	}

	toJSON(): unknown {
		const cfg: TaskCfg = new TaskCfg();
		taskCfgKeys.forEach((key) => ((cfg[key] as unknown) = this[key]));
		return cfg;
	}

	showModify() {
		if (!this.taskPropertyUi) {
			this.taskPropertyUi = new TaskPropertyUi(
				this.parent.ip,
				this.parent.token!,
				this,
				undefined,
				undefined,
				(params) => this.modify(params),
				() => (this.taskPropertyUi = undefined),
			);
		} else this.taskPropertyUi.reveal();
	}

	modify(params: TaskUpsertParams) {
		taskCfgKeys.forEach((key) => {
			if (key !== "children" && key !== "type") (this[key] as unknown) = params[key]!;
		});
		this.parent.parent.save();
	}

	async build(): Promise<boolean> {
		try {
			const cmakeProject = await ide.cmake?.getProject(
				this.parent.parent.workspaceFolder.uri,
			);
			await cmakeProject?.configure();
			await cmakeProject?.build([this.label]);
			return true;
		} catch {
			vscode.commands.executeCommand("workbench.action.problems.focus");
			return false;
		}
	}

	async upload(): Promise<boolean> {
		if (!(await this.build())) return false;
		const cmakeProject = await ide.cmake?.getProject(this.parent.parent.workspaceFolder.uri);
		const configType = await cmakeProject?.getActiveBuildType();
		const config = cmakeProject?.codeModel?.configurations.find(
			(value) => value.name === configType,
		);
		const project = config?.projects.find((value) => value.name === this.parent.parent.label);
		const target = project?.targets.find((value) => value.name === this.label);
		let libPath = ide.cmakeGetTargetArtifact(target);
		if (!libPath) {
			vscode.window.showErrorMessage("Task target not exist");
			return false;
		}
		if (!Fs.existsSync(libPath)) {
			vscode.window.showErrorMessage("Task file not exist");
			return false;
		}
		const zip = new Zip();
		zip.file(target!.fullName!, Fs.readFileSync(libPath));

		const zipLib = (libPath: string) => {
			/*let depLibLink = libPath;
			while (true) {
				zip.file(Path.basename(depLibLink), Fs.readFileSync(depLibLink));

				if (Fs.lstatSync(depLibLink, { throwIfNoEntry: false }).isSymbolicLink()) {
					depLibLink = Path.join(Path.dirname(depLibLink), Fs.readlinkSync(depLibLink));
				} else break;
			}*/

			//TODO: use DT_SONAME

			const baseName = Path.basename(libPath).substring(0, libPath.indexOf(".so") + 3);
			Fs.readdirSync(Path.dirname(libPath), { withFileTypes: true }).forEach((entry) => {
				if (entry.name.startsWith(baseName) && (entry.isFile() || entry.isSymbolicLink())) {
					zip.file(entry.name, Fs.readFileSync(Path.join(entry.parentPath, entry.name)));
				}
			});
		};

		const buildDir = await cmakeProject?.getBuildDirectory();
		const apiDir = Path.join(buildDir!, ".cmake", "api", "v1", "reply");
		const targetJsonFile = Fs.readdirSync(apiDir).find(
			(value) =>
				value.startsWith(`target-${target!.name}-${configType}-`) &&
				value.endsWith(".json"),
		);
		if (targetJsonFile) {
			const targetJson = ide.readJsonFile<{
				link?: { commandFragments?: { fragment?: string; role?: string }[] };
			}>(Path.join(apiDir, targetJsonFile));

			const libDirs: string[] = [];
			const libNames: string[] = [];
			targetJson?.link?.commandFragments?.forEach((cmd) => {
				if (!cmd.fragment) return;
				if (cmd.fragment.startsWith("-L")) {
					libDirs.push(cmd.fragment.substring(2));
				} else if (cmd.fragment.startsWith("-l")) {
					libNames.push("lib" + cmd.fragment.substring(2) + ".so");
				} else {
					const stats = Fs.lstatSync(cmd.fragment, { throwIfNoEntry: false });
					if (stats && (stats.isSymbolicLink() || stats.isFile())) zipLib(cmd.fragment);
				}
			});
			libNames.forEach((libName) => {
				for (const libDir of libDirs) {
					const libPath = Path.join(libDir, libName);
					if (Fs.existsSync(libPath)) {
						zipLib(libPath);
						break;
					}
				}
			});
		}

		(
			target as unknown as {
				dependencies?: { id: string }[];
			}
		).dependencies?.forEach((dep) => {
			const depName = dep.id.split("::")[0];
			for (const projectTmp of config!.projects) {
				const depTarget = projectTmp.targets.find(
					(targetTmp) => targetTmp.name === depName,
				);
				let depLibPath;
				if (
					depTarget &&
					depTarget.type === "SHARED_LIBRARY" &&
					(depLibPath = ide.cmakeGetTargetArtifact(depTarget))
				) {
					zipLib(depLibPath);
				}
			}
		});

		zip.file(
			"task.json",
			JSON.stringify({
				label: this.label,
				schedule: this.schedule,
				watchdog: this.watchdog,
			}),
		);

		const canConfigPath = ide.pathJoin(this.parent.parent.folder, "canBusConfig.json");
		if (Fs.existsSync(canConfigPath))
			zip.file("canBusConfig.json", Fs.readFileSync(canConfigPath));

		const zipBlob = await zip.generateAsync({
			type: "blob",
			compression: "DEFLATE",
			compressionOptions: {
				level: 5,
			},
		});

		/*const zipArray = await zipBlob.arrayBuffer();
		const zipBuf = Buffer.from(zipArray);
		Fs.writeFileSync("D:/desk/task.zip", zipBuf);*/

		const result: boolean = await jsonRpcUpload(
			apiUrl(this.parent.ip, "task"),
			new JsonRpcRequest("upload", { token: this.parent.token }),
			zipBlob,
		);
		if (!result) vscode.window.showErrorMessage("Failed to upload task");
		else vscode.window.showInformationMessage("Upload task successfully");
		return result;
	}

	async start(): Promise<boolean> {
		if (!(await this.upload())) return false;
		const result: boolean = await jsonRpcCall(
			apiUrl(this.parent.ip, "task"),
			new JsonRpcRequest("start", { token: this.parent.token, label: this.label }),
		);
		if (!result) vscode.window.showErrorMessage("Failed to start task");
		else vscode.window.showInformationMessage("Start task successfully");
		return result;
	}

	async stop() {
		const result: boolean = await jsonRpcCall(
			apiUrl(this.parent.ip, "task"),
			new JsonRpcRequest("stop", { token: this.parent.token, label: this.label }),
		);
		if (!result) vscode.window.showErrorMessage("Failed to stop task");
		else vscode.window.showInformationMessage("Stop task successfully");
	}

	async debug() {
		if (!vscode.debug.activeDebugSession) await this.start();
		await this.parent.debug();
	}

	monitor() {
		if (!this.taskMonitorUi)
			this.taskMonitorUi = new TaskMonitorUi(
				this.label,
				this.parent.ip,
				this.parent.token!,
				() => (this.taskMonitorUi = undefined),
			);
		else this.taskMonitorUi.reveal();
	}

	async remove() {
		try {
			await this.stop();
		} catch (error) {
			ide.logError(error);
		}
		await jsonRpcCall(
			apiUrl(this.parent.ip, "task"),
			new JsonRpcRequest("remove", { token: this.parent.token, label: this.label }),
		);
		this.parent.parent.removeChild(this.parent, this);
	}
}
