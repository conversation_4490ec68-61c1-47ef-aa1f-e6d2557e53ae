import vscode from "vscode";
import Fs from "fs";
import Path from "path";
import Stream from "stream";
import StreamPromise from "stream/promises";
import type { ReadableStream } from "stream/web";
import os from "node:os";

import Csv from "csv";
import CsvSync from "csv/sync";

import Dayjs from "dayjs";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";
import {
	JsonRpcRequest,
	JsonRpcNotify,
	jsonRpcCall,
	apiUrl,
	jsonRpcSubscribe,
	WebSock,
} from "@taotech/libdual";
import {
	type Log,
	type Logs,
	type GetLogsParams,
	type LogStats,
	GetLogsNotify,
	SetLogsNotify,
} from "@taotech/plc-ide-ui";

export default class PlcLogUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;
	private logFilePath: string = "";
	socket: WebSock;
	live: Logs = [];
	getParams?: GetLogsParams;
	dir: vscode.Uri;
	filesSyncing: boolean = false;

	constructor(
		public ip: string,
		public id: string,
		public token: string,
		public task?: string,
		...disposes: Disposes
	) {
		super(...disposes);
		this.panel = vscode.window.createWebviewPanel("plcLog", "PLC Log", vscode.ViewColumn.One, {
			enableScripts: true,
			enableCommandUris: true,
			retainContextWhenHidden: true,
		});
		this.willDispose(this.panel);
		this.willDispose(() => {
			if (this.socket) this.socket.close();
		});

		this.dir = vscode.Uri.joinPath(ide.context.globalStorageUri, id, "log");

		this.socket = jsonRpcSubscribe(
			apiUrl(ip, "log"),
			"List",
			{
				token,
				publishInterval: 1000,
			},
			(logs?: Logs) => {
				if (logs) this.onPublish(logs);
			},
			async (result: Array<{ url: string; time: number }>) => {
				await this.syncLogFiles(result);
			},
		);

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(msg: JsonRpcNotify<unknown>) => {
				switch (msg.method) {
					case GetLogsNotify.METHOD:
						this.onGetLogs((msg as GetLogsNotify).params!);
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private async syncLogFiles(result: Array<{ url: string; time: number }>) {
		this.filesSyncing = true;
		for (const file of result as Array<{ url: string; time: number }>) {
			const filePath = vscode.Uri.joinPath(this.dir, Path.basename(file.url));
			if (ide.fileExist(filePath)) continue;

			const url = new URL(`http://${this.ip}${file.url}`);

			try {
				const rsp = await fetch(url);
				if (!rsp.ok || !rsp.body) {
					ide.logError(`Failed to fetch log file: ${file.url}`);
					continue;
				}

				if (!ide.fileExist(this.dir)) await vscode.workspace.fs.createDirectory(this.dir);
				const readStream = Stream.Readable.fromWeb(rsp.body as ReadableStream<Uint8Array>);
				const writeStream = Fs.createWriteStream(filePath.fsPath);
				await StreamPromise.finished(readStream.pipe(writeStream));
				const fileTime = new Date(file.time);
				Fs.utimesSync(filePath.fsPath, fileTime, fileTime);
			} catch (e) {
				Fs.rmSync(filePath.fsPath);
				ide.logError(e);
			}
		}
		this.filesSyncing = false;
		await this.setLogs();
	}

	private async onPublish(newLogs: Logs) {
		this.live = this.live.concat(newLogs);
		await this.setLogs();
	}

	private async onGetLogs(getParams: GetLogsParams) {
		this.getParams = getParams;

		for (const filter of this.getParams.columnFilters) {
			if (filter.id === "time") {
				interface DayjsJson {
					$d: string;
				}
				const [begin, end] = filter.value as [DayjsJson, DayjsJson];
				filter.value = [
					begin ? Dayjs(begin.$d) : undefined,
					end ? Dayjs(end.$d) : undefined,
				];
			}
		}

		await this.setLogs();
	}

	private matchLog(log: Log): boolean {
		if (!this.getParams) return true;
		let match = true;
		for (const filter of this.getParams.columnFilters) {
			if (filter.id === "component") {
				if (filter.value && !log.component.includes(filter.value as string)) {
					match = false;
					break;
				}
			} else if (filter.id === "level") {
				if (filter.value && !(filter.value as string[]).includes(log.level)) {
					match = false;
					break;
				}
			} else if (filter.id === "time") {
				interface DateTimeJson {
					$d: string;
				}
				const [start, end] = filter.value as [Dayjs.Dayjs, Dayjs.Dayjs];
				const logTime = Dayjs(log.time);
				if ((start && logTime.isBefore(start)) || (end && logTime.isAfter(end))) {
					match = false;
					break;
				}
			} else if (filter.id === "text") {
				if (filter.value && !log.text.includes(filter.value as string)) {
					match = false;
					break;
				}
			}
		}
		return match;
	}

	private async setLogs() {
		if (!this.getParams || this.filesSyncing) return;

		const page: Logs = [];
		let matchCount = 0;
		const pagination = this.getParams!.pagination;
		const pageRowStartIndex = pagination.pageIndex * pagination.pageSize;
		const stats: LogStats = {};

		const match = (logs: Logs) => {
			for (let i = logs.length - 1; i >= 0; i--) {
				const log = logs[i];
				if (this.matchLog(log)) {
					matchCount++;
					if (!stats[log.level]) stats[log.level] = {};
					if (!stats[log.level][log.component]) stats[log.level][log.component] = 1;
					else stats[log.level][log.component]++;
					if (page.length < pagination.pageSize && matchCount > pageRowStartIndex) {
						page.push(log);
					}
				}
			}
		};

		match(this.live);

		let files = Fs.readdirSync(this.dir.fsPath)
			.map((file) => Path.join(this.dir.fsPath, file))
			.sort((a, b) => {
				return Fs.statSync(b).mtime.getTime() - Fs.statSync(a).mtime.getTime();
			});
		for (const file of files) {
			const frs = Fs.createReadStream(file);
			const parser = frs.pipe(
				Csv.parse({
					bom: true,
					columns: true,
					ltrim: true,
					rtrim: true,
					trim: true,
					skip_records_with_error: true,
					skip_empty_lines: true,
				}),
			);
			const logs: Logs = [];
			parser.on("readable", () => {
				while (true) {
					const log = parser.read() as Log;
					if (!log) break;
					log.time = Date.parse(log.time as unknown as string);
					logs.push(log);
				}
			});
			await StreamPromise.finished(parser);
			match(logs);
		}

		await this.panel.webview.postMessage(
			new SetLogsNotify({
				logs: page,
				totalRowCount: matchCount,
				stats,
			}),
		);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "assets", "index.css"]);

		const scriptUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "assets", "index.js"]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div
						id="root"
						data-ip="${this.ip}"
						data-token="${this.token}"
						${this.task ? `data-task='${this.task}'` : ""}
					></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/plc-log-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
