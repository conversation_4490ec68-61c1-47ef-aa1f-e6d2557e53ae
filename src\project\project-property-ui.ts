import vscode from "vscode";

import Fs from "fs";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";
import { ProjectUpsertNotify, ProjectUpsertParams } from "@taotech/plc-ide-ui";
import { PickFolderFailure, PickFolderReq, PickFolderSuccess } from "@taotech/libdual";

import { JsonRpcNotify } from "@taotech/libdual";

export default class ProjectPropertyUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(
		private onSucess?: (params: ProjectUpsertParams) => void,
		...disposes: Disposes
	) {
		super(...disposes);
		this.panel = vscode.window.createWebviewPanel(
			"ProjectProperty",
			"工程属性",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		this.willDispose(this.panel, () => (this.onSucess = undefined));

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(msg: JsonRpcNotify<unknown>) => {
				switch (msg.method) {
					case PickFolderReq.METHOD:
						void this.pickFolder(msg as PickFolderReq);
						return;
					case ProjectUpsertNotify.METHOD:
						if (this.onSucess) this.onSucess((msg as ProjectUpsertNotify).params);
						this.dispose();
						return;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private async pickFolder(req: PickFolderReq) {
		while (true) {
			const uri = await vscode.window.showOpenDialog({
				canSelectFolders: true,
				canSelectFiles: false,
				canSelectMany: false,
			});
			if (uri && uri[0]) {
				if (Fs.readdirSync(uri[0].fsPath).length === 0) {
					void this.panel.webview.postMessage(new PickFolderSuccess(req, uri[0].fsPath));
					break;
				} else if (
					"Retry" !==
					(await vscode.window.showWarningMessage(
						"The directory is not empty",
						{ modal: true },
						"Retry",
					))
				)
					break;
			} else {
				void this.panel.webview.postMessage(new PickFolderFailure(req, "no folder picked"));
				break;
			}
		}
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root" ${`data-toolchains='${JSON.stringify(ide.toolchains)}'`}></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/project-property-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
