import { useState } from "react";
import { <PERSON>, Form, Button, Divider } from "@arco-design/web-react";
import { IconDelete, IconUpload } from "@arco-design/web-react/icon";
import { vscodeWeb } from "@taotech/libweb";

export default function App() {
	interface CanScanMessage {
		loading: boolean;
		name: string;
		supplier: string;
		category: string;
		version: string;
		within: number;
	}

	interface CanScanMessageEvent {
		data: {
			command: string;
			data: CanScanMessage;
		};
	}

	const scanMessage: CanScanMessage = {
		loading: true,
		name: "扫描中...",
		supplier: "扫描中...",
		category: "扫描中...",
		version: "扫描中...",
		within: 0,
	};

	const [ScanMessage, setcanScanMessage] = useState(scanMessage);

	window.addEventListener("message", (event: CanScanMessageEvent) => {
		const message = event.data;
		if (message.command === "CanScanMessage") {
			setcanScanMessage(message.data);
		}
	});

	const handleAdd = () => {
		vscodeWeb.postMessage({ command: "scan", data: "add" });
	};

	const handleCancel = () => {
		vscodeWeb.postMessage({ command: "scan", data: "cancel" });
	};

	const handleAddDevice = () => {
		vscodeWeb.postMessage({ command: "scan", data: "addDevice" });
	};

	return (
		<Spin
			tip="This may take a while..."
			loading={ScanMessage.loading}
			style={{ display: "block", marginTop: 8, width: "400px", height: "300" }}
		>
			<Divider orientation="left">设备信息</Divider>
			<Form>
				<Form.Item>名称: {ScanMessage.name}</Form.Item>
				<Form.Item>供应商: {ScanMessage.supplier}</Form.Item>
				<Form.Item>类别: {ScanMessage.category}</Form.Item>
				<Form.Item>版本: {ScanMessage.version}</Form.Item>
			</Form>
			{!ScanMessage.loading && (
				<div>
					{ScanMessage.within === 0 && (
						<div>
							<Button
								type="secondary"
								icon={<IconUpload />}
								onClick={() => handleAdd()}
							>
								添加
							</Button>
							<Button
								type="secondary"
								icon={<IconDelete />}
								onClick={() => handleCancel()}
							>
								取消
							</Button>
						</div>
					)}

					{ScanMessage.within === 1 && (
						<div>
							<Divider orientation="left">设备库中不存在该设备，是否添加？</Divider>
							<Button
								type="secondary"
								icon={<IconUpload />}
								onClick={() => handleAddDevice()}
							>
								添加设备
							</Button>
							<Button
								type="secondary"
								icon={<IconDelete />}
								onClick={() => handleCancel()}
							>
								取消
							</Button>
						</div>
					)}
					{ScanMessage.within === 2 && (
						<div>
							<Divider orientation="left">总线上无设备</Divider>
							<Button
								type="secondary"
								icon={<IconDelete />}
								onClick={() => handleCancel()}
							>
								取消
							</Button>
						</div>
					)}
				</div>
			)}
		</Spin>
	);
}
