import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";

import PlcItem from "../plc/plc-item";

import CrrcPcmUi from "./crrc-pcm-ui";
import CrrcRbmUi from "./crrc-rbm-ui";

import CrrcADItem from "./crrc-ad-item";
import { type CrrcRacksADsParams, CrrcADParams } from "@taotech/plc-ide-ui";

import CrrcDIItem from "./crrc-di-item";
import { type CrrcRacksDIsParams, CrrcDIParams } from "@taotech/plc-ide-ui";

import CrrcDOItem from "./crrc-do-item";
import { type CrrcRacksDOsParams, CrrcDOParams } from "@taotech/plc-ide-ui";

import CrrcTIItem from "./crrc-ti-item";
import { type CrrcRacksTIsParams, CrrcTIParams } from "@taotech/plc-ide-ui";

import CrrcCANItem from "./crrc-can-item";
import { type CrrcRacksCANsParams, CrrcCANParams } from "@taotech/plc-ide-ui";

export class CrrcRackCfg {
	children?: VscodeTreeItem[];
	constructor(public index: number) {}
}

export default class CrrcRackItem extends VscodeTreeItem {
	declare parent: PlcItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-rack";
	command = {
		title: "Property",
		command: "taotech.crrc.rack.edit",
		arguments: [this as unknown],
	};

	crrcRackUi?: CrrcPcmUi | CrrcRbmUi;

	constructor(
		public cfg: CrrcRackCfg,
		parent: VscodeTreeItem,
	) {
		super(CrrcRackItem.name, cfg.index === 0 ? "PCM(CPU)" : "RBM " + cfg.index, parent);
		this.disposer.willDispose(() => {
			if (this.crrcRackUi) this.crrcRackUi.dispose();
		});

		cfg.children?.forEach((child) => {
			switch (child.type) {
				case CrrcADParams.name:
					new CrrcADItem(child as unknown as CrrcADParams, this);
					break;
				case CrrcDIParams.name:
					new CrrcDIItem(child as unknown as CrrcDIParams, this);
					break;
				case CrrcDOParams.name:
					new CrrcDOItem(child as unknown as CrrcDOParams, this);
					break;
				case CrrcTIParams.name:
					new CrrcTIItem(child as unknown as CrrcTIParams, this);
					break;
				case CrrcCANParams.name:
					new CrrcCANItem(child as unknown as CrrcCANParams, this);
					break;
			}
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	remove() {
		this.parent.parent.removeChild(this.parent, this);
	}

	showEdit() {
		if (!this.crrcRackUi) {
			this.crrcRackUi =
				this.cfg.index === 0
					? new CrrcPcmUi(this.label, () => (this.crrcRackUi = undefined))
					: new CrrcRbmUi(this.label, () => (this.crrcRackUi = undefined));
		} else this.crrcRackUi.reveal();
	}

	getChildFreeIndexies(extra: number | undefined = undefined) {
		return Array.from({ length: 32 }, (e, i) => i + 1).filter(
			(e, i) =>
				e === extra ||
				!this.children.some(
					(child) => e === (child as unknown as { cfg: { index: number } }).cfg.index,
				),
		);
	}

	getChildFreeIndex() {
		const indexies = this.getChildFreeIndexies();

		if (indexies.length === 0) {
			vscode.window.showErrorMessage(
				"There are no empty nodes, all modules have been added.",
			);
			return undefined;
		}

		return indexies[0];
	}

	isChildIndexValidForEdit(newIndex: number, selfIndex: number) {
		const indexies = this.getChildFreeIndexies(selfIndex);
		const valid = this.getChildFreeIndexies(selfIndex).includes(newIndex);
		if (!valid) vscode.window.showErrorMessage(`Module ${newIndex} already exists`);
		return valid;
	}

	showAddAD() {
		const index = this.getChildFreeIndex();
		if (index === undefined) return;

		const item = new CrrcADItem(new CrrcADParams(index), this);
		ide.solution.refresh(this);
		this.parent.parent.save();
		item.showEdit();
	}

	showAddDI() {
		const index = this.getChildFreeIndex();
		if (index === undefined) return;

		const item = new CrrcDIItem(new CrrcDIParams(index), this);
		ide.solution.refresh(this);
		this.parent.parent.save();
		item.showEdit();
	}

	showAddDO() {
		const index = this.getChildFreeIndex();
		if (index === undefined) return;

		const item = new CrrcDOItem(new CrrcDOParams(index), this);
		ide.solution.refresh(this);
		this.parent.parent.save();
		item.showEdit();
	}

	showAddTI() {
		const index = this.getChildFreeIndex();
		if (index === undefined) return;

		const item = new CrrcTIItem(new CrrcTIParams(index), this);
		ide.solution.refresh(this);
		this.parent.parent.save();
		item.showEdit();
	}

	showAddCan() {
		const index = this.getChildFreeIndex();
		if (index === undefined) return;

		const item = new CrrcCANItem(new CrrcCANParams(index), this);
		ide.solution.refresh(this);
		this.parent.parent.save();
		item.showEdit();
	}
}
