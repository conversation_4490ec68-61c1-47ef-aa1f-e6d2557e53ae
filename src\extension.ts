import vscode from "vscode";

import Fs from "fs";
import Process from "process";

import Disposer from "./disposer";
import Solution from "./solution";
import PlcItem from "./plc/plc-item";
import ProjectItem from "./project/project-item";
import TaskSrcItem from "./task/task-src-item";
import TaskItem from "./task/task-item";
import * as cmaketools from "vscode-cmake-tools";

import pkg from "../package.json";
import { edsDeviceAdd } from "./bus-manager/bus-manager-ui";
import BusManagerItem from "./bus-manager/bus-manager-item";
import CanMasterManagerItem from "./bus-manager/can-master-manager-item";
import CanSlaveManagerItem from "./bus-manager/can-slave-manager-item";
import { parseJson } from "@taotech/libdual";
import CrrcRackItem from "./crrc/crrc-rack-item";
import CrrcADItem from "./crrc/crrc-ad-item";
import CrrcDIItem from "./crrc/crrc-di-item";
import CrrcDOItem from "./crrc/crrc-do-item";
import CrrcTIItem from "./crrc/crrc-ti-item";
import CrrcCANItem from "./crrc/crrc-can-item";
import CrrcModbusRtuMasterItem from "./crrc/crrc-modbus-rtu-master-item";
import CrrcModbusRtuSlaveItem from "./crrc/crrc-modbus-rtu-slave-item";
import CrrcModbusTcpClientItem from "./crrc/crrc-modbus-tcp-client-item";
import CrrcModbusTcpServerItem from "./crrc/crrc-modbus-tcp-server-item";
import { type Toolchains } from "@taotech/plc-ide-ui";

const projectFileName = pkg.activationEvents[0].split(":")[1];

export let ide: Ide;

export async function activate(context: vscode.ExtensionContext) {
	const log = vscode.window.createOutputChannel("TaoTechPLCIDE", { log: true });
	log.info("activate, log level:", log.logLevel);
	ide = new Ide(context, log);
	await ide.init();
	vscode.commands.executeCommand("taotech-view-solution.focus");
}

export function deactivate() {
	ide.log.info("deactivate");
	ide.dispose();
	ide = undefined!;
}

class Ide extends Disposer {
	solution = new Solution();
	pkg = pkg;
	projectFileName = projectFileName;
	appDir: string;
	cmake?: cmaketools.CMakeToolsApi;
	toolchains: Toolchains = {};

	constructor(
		public context: vscode.ExtensionContext,
		public log: vscode.LogOutputChannel,
	) {
		super();
		this.willDispose(this.log, this.solution);

		this.appDir = this.pathJoin(Process.env.LOCALAPPDATA!, "Programs/TaoTechPLCIDE");

		Fs.readdirSync(this.pathJoin(this.appDir, "compiler"), {
			withFileTypes: true,
		}).forEach((dirent) => {
			try {
				const toolchain = Fs.readFileSync(
					this.pathJoin(this.appDir, "compiler", dirent.name, "toolchain.cmake"),
					{
						encoding: "utf8",
					},
				);
				const cStandards = toolchain.match(
					/^\s*set\s*\(\s*SUPPORTED_C_STANDARDS((\s+[0-9]{2})+)\s*\)\s*$/m,
				);
				const cxxStandards = toolchain.match(
					/^\s*set\s*\(\s*SUPPORTED_CXX_STANDARDS((\s+[0-9]{2})+)\s*\)\s*$/m,
				);
				this.toolchains[dirent.name] = {
					SUPPORTED_C_STANDARDS: cStandards
						? cStandards[1].split(/\s+/).filter(Boolean)
						: [],
					SUPPORTED_CXX_STANDARDS: cxxStandards
						? cxxStandards[1].split(/\s+/).filter(Boolean)
						: [],
				};
			} catch (error) {
				this.logError(error);
			}
		});
	}

	async init() {
		this.cmake = await cmaketools.getCMakeToolsApi(cmaketools.Version.v3);
		this.solution.init();
		this.registerCommands();
	}

	registerCommands() {
		vscode.commands.executeCommand(
			"setContext",
			"taotech.showDeviceMenu",
			ide.pkg.taotech.showDeviceMenu,
		);

		this.registerCommand("taotech.project.new", () => this.solution.showNewProject());
		this.registerCommand("taotech.plc.scan", async (item) => (item as ProjectItem).scanPlc());
		this.registerCommand("taotech.plc.add", async (item) => (item as ProjectItem).inputPlc());
		this.registerCommand("taotech.device.add", edsDeviceAdd);

		this.registerCommand("taotech.plc.remove", (item) => (item as PlcItem).remove());
		this.registerCommand("taotech.plc.login", async (item) => await (item as PlcItem).login());
		this.registerCommand("taotech.plc.monitor", (item) => (item as PlcItem).monitor());
		this.registerCommand("taotech.plc.variables", (item) => (item as PlcItem).variables());
		this.registerCommand("taotech.plc.trace", (item) => (item as PlcItem).showTrace());
		this.registerCommand("taotech.plc.log", (item) => (item as PlcItem).log());
		this.registerCommand("taotech.plc.debug", async (item) => (item as PlcItem).debug());
		this.registerCommand("taotech.plc.terminal", async (item) => (item as PlcItem).terminal());
		this.registerCommand("taotech.plc.files", async (item) => await (item as PlcItem).files());
		this.registerCommand("taotech.tasksrc.new", async (item) =>
			(item as ProjectItem).newTaskSrc(),
		);
		this.registerCommand("taotech.tasksrc.remove", async (item) =>
			(item as TaskSrcItem).remove(),
		);
		this.registerCommand("taotech.tasksrc.matlab", async (item) =>
			(item as TaskSrcItem).matlab(),
		);
		this.registerCommand("taotech.tasksrc.build", async (item) =>
			(item as TaskSrcItem).build(),
		);
		this.registerCommand("taotech.task.add", (item) => (item as PlcItem).showAddTask());
		this.registerCommand("taotech.task.modify", (item) => (item as TaskItem).showModify());
		this.registerCommand("taotech.task.build", (item) => (item as TaskItem).build());
		this.registerCommand("taotech.task.upload", async (item) => (item as TaskItem).upload());
		this.registerCommand("taotech.task.start", async (item) => (item as TaskItem).start());
		this.registerCommand("taotech.task.stop", async (item) => (item as TaskItem).stop());
		this.registerCommand("taotech.task.monitor", (item) => (item as TaskItem).monitor());
		this.registerCommand("taotech.task.debug", async (item) => (item as TaskItem).debug());
		this.registerCommand("taotech.task.remove", async (item) => (item as TaskItem).remove());

		this.registerCommand("taotech.plc.bus.add", (item) => (item as PlcItem).showAddBus());
		this.registerCommand("taotech.bus.manager.del", (item) =>
			(item as BusManagerItem).remove(),
		);
		this.registerCommand("taotech.bus.manager.edit", (item) =>
			(item as BusManagerItem).showModify(),
		);
		this.registerCommand("taotech.bus.manager.add", (item) =>
			(item as BusManagerItem).showAddCanMasterManager(),
		);

		this.registerCommand("taotech.bus.can.master.del", (item) =>
			(item as CanMasterManagerItem).remove(),
		);
		this.registerCommand("taotech.bus.can.master.edit", (item) =>
			(item as CanMasterManagerItem).showModify(),
		);
		this.registerCommand("taotech.bus.can.master.add", (item) =>
			(item as CanMasterManagerItem).showAddSlave(),
		);
		this.registerCommand("taotech.bus.can.master.scan", (item) =>
			(item as CanMasterManagerItem).showScanSlave(),
		);

		this.registerCommand("taotech.bus.can.slave.del", (item) =>
			(item as CanSlaveManagerItem).remove(),
		);
		this.registerCommand("taotech.bus.can.slave.edit", (item) =>
			(item as CanSlaveManagerItem).showModify(),
		);

		this.registerCommand("taotech.crrc.upsert", async (item) =>
			(item as PlcItem).crrcUpsertParams(),
		);

		this.registerCommand("taotech.crrc.modbus.rtu.master.add", async (item) =>
			(item as PlcItem).crrcAddModbusRtuMaster(),
		);
		this.registerCommand("taotech.crrc.modbus.rtu.master.edit", async (item) =>
			(item as CrrcModbusRtuMasterItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.modbus.rtu.master.remove", async (item) =>
			(item as CrrcModbusRtuMasterItem).remove(),
		);
		this.registerCommand("taotech.crrc.modbus.rtu.slave.add", async (item) =>
			(item as PlcItem).crrcAddModbusRtuSlave(),
		);
		this.registerCommand("taotech.crrc.modbus.rtu.slave.edit", async (item) =>
			(item as CrrcModbusRtuSlaveItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.modbus.rtu.slave.remove", async (item) =>
			(item as CrrcModbusRtuSlaveItem).remove(),
		);

		this.registerCommand("taotech.crrc.modbus.tcp.client.add", async (item) =>
			(item as PlcItem).crrcAddModbusTcpClient(),
		);
		this.registerCommand("taotech.crrc.modbus.tcp.client.edit", async (item) =>
			(item as CrrcModbusTcpClientItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.modbus.tcp.client.remove", async (item) =>
			(item as CrrcModbusTcpClientItem).remove(),
		);
		this.registerCommand("taotech.crrc.modbus.tcp.server.add", async (item) =>
			(item as PlcItem).crrcAddModbusTcpServer(),
		);
		this.registerCommand("taotech.crrc.modbus.tcp.server.edit", async (item) =>
			(item as CrrcModbusTcpServerItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.modbus.tcp.server.remove", async (item) =>
			(item as CrrcModbusTcpServerItem).remove(),
		);
		this.registerCommand("taotech.crrc.rack.add", async (item) =>
			(item as PlcItem).crrcAddRack(),
		);
		this.registerCommand("taotech.crrc.rack.edit", async (item) =>
			(item as CrrcRackItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.rack.remove", async (item) =>
			(item as CrrcRackItem).remove(),
		);
		this.registerCommand("taotech.crrc.ad.add", async (item) =>
			(item as CrrcRackItem).showAddAD(),
		);
		this.registerCommand("taotech.crrc.ad.edit", async (item) =>
			(item as CrrcADItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.ad.remove", async (item) =>
			(item as CrrcADItem).remove(),
		);
		this.registerCommand("taotech.crrc.di.add", async (item) =>
			(item as CrrcRackItem).showAddDI(),
		);
		this.registerCommand("taotech.crrc.di.edit", async (item) =>
			(item as CrrcDIItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.di.remove", async (item) =>
			(item as CrrcDIItem).remove(),
		);
		this.registerCommand("taotech.crrc.do.add", async (item) =>
			(item as CrrcRackItem).showAddDO(),
		);
		this.registerCommand("taotech.crrc.do.edit", async (item) =>
			(item as CrrcDOItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.do.remove", async (item) =>
			(item as CrrcDOItem).remove(),
		);
		this.registerCommand("taotech.crrc.ti.add", async (item) =>
			(item as CrrcRackItem).showAddTI(),
		);
		this.registerCommand("taotech.crrc.ti.edit", async (item) =>
			(item as CrrcTIItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.ti.remove", async (item) =>
			(item as CrrcTIItem).remove(),
		);
		this.registerCommand("taotech.crrc.can.add", async (item) =>
			(item as CrrcRackItem).showAddCan(),
		);
		this.registerCommand("taotech.crrc.can.edit", async (item) =>
			(item as CrrcCANItem).showEdit(),
		);
		this.registerCommand("taotech.crrc.can.remove", async (item) =>
			(item as CrrcCANItem).remove(),
		);
	}

	registerCommand(command: string, callback: (...args: unknown[]) => unknown, thisArg?: unknown) {
		this.willDispose(vscode.commands.registerCommand(command, callback, thisArg));
	}

	getNonce() {
		let text = "";
		const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		for (let i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	}

	getWebviewUri(webview: vscode.Webview, pathList: string[]) {
		return webview.asWebviewUri(vscode.Uri.joinPath(this.context.extensionUri, ...pathList));
	}

	isProductionMode() {
		return this.context.extensionMode === vscode.ExtensionMode.Production;
	}

	errorToString(error: unknown): string {
		if (typeof error === "string") return error;
		else if (
			typeof error === "object" &&
			error !== null &&
			"message" in error &&
			typeof (error as Record<string, unknown>).message === "string"
		)
			return error.message as string;
		else {
			try {
				return JSON.stringify(error);
			} catch {
				return String(error);
			}
		}
	}

	logError(error: unknown, ...args: unknown[]) {
		this.log.error(this.errorToString(error), ...args);
	}

	replaceFile(path: string, patterns: (string | RegExp)[], replacements: string[]) {
		let content = Fs.readFileSync(path, { encoding: "utf8" });
		patterns.forEach((pattern, index) => {
			content = content.replaceAll(pattern, replacements[index]);
		});
		Fs.writeFileSync(path, content);
	}

	readJsonFile<Json>(path?: string): Json | undefined {
		if (!path) return undefined;
		const content = Fs.readFileSync(path, {
			encoding: "utf8",
		});
		return parseJson<Json>(content);
	}

	writeJsonFile(path: string, json: unknown) {
		Fs.writeFileSync(path, JSON.stringify(json, null, "\t"));
	}

	modifyJsonFile<Json>(path: string, modifer: (json: Json) => void) {
		const json = this.readJsonFile<Json>(path);
		if (json) {
			modifer(json);
			this.writeJsonFile(path, json);
		}
	}

	renameSync(oldPath: Fs.PathLike, newPath: Fs.PathLike) {
		if (Fs.existsSync(oldPath) /*&& !fs.existsSync(newPath)*/) Fs.renameSync(oldPath, newPath);
	}

	pathJoin(base: string, ...paths: string[]) {
		return vscode.Uri.joinPath(vscode.Uri.file(base), ...paths).fsPath;
	}

	async fileCopy(source: vscode.Uri, target: vscode.Uri) {
		return vscode.workspace.fs.copy(source, target);
	}

	async fileReadString(uri: vscode.Uri) {
		const content = await vscode.workspace.fs.readFile(uri);
		return new TextDecoder().decode(content);
	}

	async fileWriteString(uri: vscode.Uri, text: string) {
		const content = new TextEncoder().encode(text);
		await vscode.workspace.fs.writeFile(uri, content);
	}

	async fileReplace(uri: vscode.Uri, patterns: (string | RegExp)[], replacements: string[]) {
		let text = await this.fileReadString(uri);
		patterns.forEach((pattern, index) => {
			text = text.replaceAll(pattern, replacements[index]);
		});
		await this.fileWriteString(uri, text);
	}

	fileExist(uri: vscode.Uri) {
		return Fs.existsSync(uri.fsPath);
	}

	async fileRename(source: vscode.Uri, target: vscode.Uri) {
		if (this.fileExist(source) /*&& !fileExist(target)*/)
			await vscode.workspace.fs.rename(source, target, { overwrite: false });
	}

	onWindows() {
		return Process.platform === "win32";
	}

	onLinux() {
		return Process.platform === "linux";
	}

	// https://github.com/microsoft/vscode-cmake-tools-api/issues/7
	// https://github.com/microsoft/vscode-cmake-tools/issues/3015
	cmakeGetTargetArtifact(target?: cmaketools.CodeModel.Target): string | undefined {
		//TODO: linux path separator
		return target?.artifacts?.at(0)?.replace("\\" + target.name, "");
	}
}
