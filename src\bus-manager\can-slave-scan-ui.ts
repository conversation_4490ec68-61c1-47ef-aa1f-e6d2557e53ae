import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import BusManagerItem from "./bus-manager-item";
import { CanSlaveManagerCfg } from "./can-slave-manager-item";
import BusManagerUi, {
	AddDeviceInfoGet,
	BUSEDITTYPE,
	DeviceInfo,
	edsDeviceAdd,
	optionUpdate,
} from "./bus-manager-ui";
import { apiUrl, jsonRpcCall, JsonRpcRequest } from "@taotech/libdual";
import { CanSlaveGetResult } from "./bus-type";

const counter = 20; //扫描超时 时间（seconds）
let productNumber: number;
let revisionNumber: number;
let vendorNumber: number;
let nodeId: number;

export default class CanSlaveScanUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;
	busManagerItem: BusManagerItem;
	busManagerUi: BusManagerUi;
	intervalId: NodeJS.Timeout;
	constructor(
		busManagerUi: BusManagerUi,
		busManagerItem: BusManagerItem,
		private onAdd?: (params: CanSlaveManagerCfg) => void,
		...disposes: Disposes
	) {
		super(...disposes);

		this.busManagerItem = busManagerItem;
		this.busManagerUi = busManagerUi;

		this.panel = vscode.window.createWebviewPanel(
			"canSlaveScan",
			"扫描设备",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);

		this.willDispose(this.panel, this.busManagerUi, () => clearInterval(this.intervalId));

		void this.panel.webview.postMessage({
			command: "CanScanMessage",
			data: {
				loading: true,
				name: "扫描中...",
				supplier: "扫描中...",
				category: "扫描中...",
				version: "扫描中...",
				within: false,
			},
		});

		jsonRpcCall<CanSlaveGetResult>(
			apiUrl(this.busManagerItem.parent.ip, "bus/can"),
			new JsonRpcRequest("getSlave/" + this.busManagerItem.index),
		)
			.then((result: CanSlaveGetResult) => {
				optionUpdate(BUSEDITTYPE.BUSEDITTYPE_OTHER);
				this.updateStatus(result);
			})
			.catch((error: Error) => {
				ide.logError(error);
				this.updateStatus(undefined);
			});

		let i = 0;
		this.intervalId = setInterval(() => {
			i++;
			if (i >= counter) {
				clearInterval(this.intervalId); // 停止定时器
				void this.panel.webview.postMessage({
					command: "CanScanMessage",
					data: {
						loading: false,
						name: "无从站设备",
						supplier: "无从站设备",
						category: "无从站设备",
						version: "无从站设备",
						within: 2,
					},
				});

				return;
			}
		}, 1000);

		this.render();

		this.panel.webview.onDidReceiveMessage(
			async (message: { command: string; data: string }) => {
				switch (message.command) {
					case "scan":
						switch (message.data) {
							case "add":
								{
									const [ret, deviceInfo] = AddDeviceInfoGet(
										productNumber,
										revisionNumber,
										vendorNumber,
									) as [boolean, DeviceInfo];
									if (ret) {
										try {
											await this.busManagerUi.addSlave(
												deviceInfo.productName,
												deviceInfo.filePath,
												true,
												nodeId,
											);
										} catch (error) {
											void error;
											this.dispose();
										}
									}
								}
								this.dispose();
								break;
							case "cancel":
								this.dispose();
								break;
							case "addDevice":
								edsDeviceAdd();
								this.dispose();
								break;
						}
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidChangeViewState(
			() => {
				if (this.panel?.visible) this.render();

				void this.panel.webview.postMessage({
					command: "CanScanMessage",
					data: {
						loading: true,
						name: "扫描中...",
						supplier: "扫描中...",
						category: "扫描中...",
						version: "扫描中...",
						within: 0,
					},
				});
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(
			() => {
				this.dispose();
			},
			null,
			this.disposables,
		);
	}

	updateStatus(result?: CanSlaveGetResult) {
		clearInterval(this.intervalId); // 停止定时器
		if (result && result.size) {
			for (const slave of result.slaves) {
				const [ret, deviceInfo] = AddDeviceInfoGet(
					slave.productCode,
					slave.revisionNumber,
					slave.vendorId,
				) as [boolean, DeviceInfo];
				if (ret) {
					productNumber = slave.productCode;
					revisionNumber = slave.revisionNumber;
					vendorNumber = slave.vendorId;
					nodeId = slave.nodeId;
					void this.panel.webview.postMessage({
						command: "CanScanMessage",
						data: {
							loading: false,
							name: deviceInfo.productName,
							supplier: deviceInfo.vendorName,
							category: "CAN总线-从设备",
							version:
								deviceInfo.eDSVersion +
								"." +
								deviceInfo.fileVersion +
								"." +
								deviceInfo.fileRevision,
							within: 0,
						},
					});
					break;
				}

				void this.panel.webview.postMessage({
					command: "CanScanMessage",
					data: {
						loading: false,
						name: "unknown",
						supplier: "unknown",
						category: "无设备描述文件，请添加！",
						version: "unknown",
						within: 1,
					},
				});
				break;
			}
			return;
		}

		void this.panel.webview.postMessage({
			command: "CanScanMessage",
			data: {
				loading: false,
				name: "无从站设备",
				supplier: "无从站设备",
				category: "无从站设备",
				version: "无从站设备",
				within: 2,
			},
		});
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/can-slave-scan-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
