import Fs from "fs";
import Path from "path";

import { ide } from "../extension";
import { ProjectUpsertParams } from "@taotech/plc-ide-ui";
import { upsertTaskSrc } from "../task/task-src";
import ProjectItem from "./project-item";
import TaskSrcItem from "../task/task-src-item";

export function upsertProjectSrc(project: ProjectUpsertParams, update: boolean) {
	const templatePath = ide.context.asAbsolutePath("template");
	const projectTemplatePath = ide.pathJoin(templatePath, "project");

	if (!update)
		Fs.cpSync(projectTemplatePath, project.folder, {
			recursive: true,
			mode: Fs.constants.COPYFILE_EXCL,
			force: false,
			errorOnExist: true,
		});

	ide.modifyJsonFile<{
		configurePresets: {
			name: string;
			toolchainFile: string;
			cacheVariables: {
				CMAKE_C_COMPILER?: string;
				CMAKE_CXX_COMPILER?: string;
			};
		}[];
	}>(ide.pathJoin(project.folder, "CMakePresets.json"), (presetsJson) => {
		for (const preset of presetsJson.configurePresets) {
			if (preset.name === "Base")
				preset.toolchainFile =
					"$penv{LOCALAPPDATA}/Programs/TaoTechPLCIDE/compiler/" +
					project.toolchain +
					"/toolchain.cmake";
			if (!project.languages.C.enabled) delete preset.cacheVariables.CMAKE_C_COMPILER;
			if (!project.languages.CXX.enabled) delete preset.cacheVariables.CMAKE_CXX_COMPILER;
		}
	});

	Fs.writeFileSync(
		ide.pathJoin(project.folder, "variables.cmake"),
		`set(PLC_PROJECT_NAME ${Path.basename(project.folder)})
set(PLC_LANGUAGES ${project.languages.C.enabled ? "C" : ""} ${project.languages.CXX.enabled ? "CXX" : ""})
set(CMAKE_C_STANDARD ${project.languages.C.standard})
set(CMAKE_C_STANDARD_REQUIRED OFF)
set(CMAKE_CXX_STANDARD ${project.languages.CXX.standard})
set(CMAKE_CXX_STANDARD_REQUIRED OFF)
set(PLC_SOURCE_EXTENSIONS ${project.languages.C.enabled ? "c" : ""} ${project.languages.CXX.enabled ? "cpp cxx cc" : ""})
set(PLC_TASKS )
`,
	);

	project.children?.forEach((task) => upsertTaskSrc(project, task, update));

	ide.writeJsonFile(ide.pathJoin(project.folder, ide.projectFileName), project);

	upsertProjectSrcTasks(project);
}

export function upsertProjectSrcTasks(project: ProjectItem | ProjectUpsertParams) {
	const taskSrcs = project.children?.filter(
		(child) => !("type" in child) || child.type === TaskSrcItem.name,
	);

	let taskLabels = taskSrcs?.map((child) => child.label).join(" ");
	if (!taskLabels) taskLabels = "";
	ide.replaceFile(
		ide.pathJoin(project.folder, "variables.cmake"),
		[/set\(PLC_TASKS .+/gm],
		[`set(PLC_TASKS ${taskLabels})`],
	);
}
