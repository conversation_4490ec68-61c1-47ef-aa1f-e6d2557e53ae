import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcModbusTcpServerUi from "./crrc-modbus-tcp-server-ui";
import {
	type CrrcPlcModbusTcpServerParams,
	CrrcModbusTcpServerParams,
} from "@taotech/plc-ide-ui";

export default class CrrcModbusTcpServerItem extends VscodeTreeItem {
	declare parent: PlcItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-modbus-tcp-server";
	command = {
		title: "Property",
		command: "taotech.crrc.modbus.tcp.server.edit",
		arguments: [this as unknown],
	};

	crrcModbusTcpServerUi?: CrrcModbusTcpServerUi;

	constructor(
		public cfg: CrrcModbusTcpServerParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "Modbus TCP Slave", parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcModbusTcpServerUi) this.crrcModbusTcpServerUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcModbusTcpServerUi) {
			this.toJSON();
			this.crrcModbusTcpServerUi = new CrrcModbusTcpServerUi(
				this.parent.ip,
				this.parent.token!,
				[this.cfg],
				(params) => this.edit(params),
				() => (this.crrcModbusTcpServerUi = undefined),
			);
		} else this.crrcModbusTcpServerUi.reveal();
	}

	edit(params: CrrcPlcModbusTcpServerParams) {
		this.cfg = params[0];
		ide.solution.refresh(this);
		this.parent.parent.save();
	}

	remove() {
		this.parent.parent.removeChild(this.parent, this);
	}
}
