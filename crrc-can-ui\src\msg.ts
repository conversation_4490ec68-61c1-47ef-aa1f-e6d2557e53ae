import { JsonRpcRequest } from "@taotech/libdual";

export class CrrcCanPdoParams {
	constructor(
		public id: number,
		public direction: number,
		public channel: number,
		public type: number,
		public type2: number,
		public len:number,
		public trigger: number,
		public cycle: number,
		public remark: string,
		public key: string,
	) {}
}


export class CrrcCANParams {
	constructor(
		public index: number = 0,
		public moduleTyppe = 0,
		public enableType = 0,
		public can1Baudrate = 1,
		public can2Baudrate = 1,
		public pdos = [] as CrrcCanPdoParams[],
		public type = CrrcCANParams.name,
	) {}
}

export interface CrrcRackCANsParams {
	index: number;
	children: CrrcCANParams[];
}

export interface CrrcRacksCANsParams extends Array<CrrcRackCANsParams> {}

export class CrrcUpsertCANReq extends JsonRpcRequest<CrrcRacksCANsParams> {
	static METHOD = "upsert";
	declare params: CrrcRacksCANsParams;
	constructor(params: CrrcRacksCANsParams, token?: string) {
		super(CrrcUpsertCANReq.METHOD, params, token);
	}
}
