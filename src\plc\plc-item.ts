import vscode from "vscode";

import Crypto from "crypto";
import Child<PERSON>ro<PERSON> from "child_process";
import Util from "util";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import ProjectItem from "../project/project-item";
import { JsonRpcRequest, jsonRpcCall, apiUrl, jsonRpcSubscribe, WebSock } from "@taotech/libdual";
import PlcMonitorUi from "./plc-monitor-ui";
import TaskPropertyUi from "../task/task-property-ui";
import TaskItem, { TaskCfg } from "../task/task-item";
import TaskSrcItem from "../task/task-src-item";
import { TaskUpsertParams } from "@taotech/plc-ide-ui";
import { upsertTaskSrc } from "../task/task-src";
import PlcTraceUi from "./plc-trace-ui";
import PlcLogUi from "./plc-log-ui";
import PlcVariablesUi from "../plc/plc-variables-ui";
import BusManagerUi, { BUSEDITTYPE } from "../bus-manager/bus-manager-ui";
import BusManagerItem, { BusManagerCfg } from "../bus-manager/bus-manager-item";
import { busNumMax } from "../bus-manager/bus-type";
import { canBusConfigFileSave } from "../bus-manager/can-slave-manager-ui";
import { upsertProjectSrcTasks } from "../project/project-src";
import { type TraceCfg } from "@taotech/plc-ide-ui";
import CrrcRackItem, { CrrcRackCfg } from "../crrc/crrc-rack-item";
import { CrrcUpsertADReq } from "@taotech/plc-ide-ui";
import type { Summary, Task } from "@taotech/plc-ide-ui";
import CrrcModbusRtuMasterItem from "../crrc/crrc-modbus-rtu-master-item";
import { CrrcModbusRtuMasterParams } from "@taotech/plc-ide-ui";
import CrrcModbusRtuSlaveItem from "../crrc/crrc-modbus-rtu-slave-item";
import { CrrcModbusRtuSlaveParams } from "@taotech/plc-ide-ui";
import CrrcModbusTcpClientItem from "../crrc/crrc-modbus-tcp-client-item";
import { CrrcModbusTcpClientParams } from "@taotech/plc-ide-ui";
import CrrcModbusTcpServerItem from "../crrc/crrc-modbus-tcp-server-item";
import { CrrcModbusTcpServerParams } from "@taotech/plc-ide-ui";
import CrrcCANItem from "../crrc/crrc-can-item";

type PlcStatus = "tao-plc-off" | "tao-plc-on" | "tao-plc-login";

export class PlcCfg {
	type?: string;
	label?: string;
	children?: VscodeTreeItem[];
	ip!: string;
	id?: string;
	username?: string;
	trace?: TraceCfg;
	busNum?: number;
}
const plcCfgKeys = Object.keys(new PlcCfg()) as Array<keyof PlcCfg>;

export default class PlcItem extends VscodeTreeItem implements PlcCfg {
	iconPath = new vscode.ThemeIcon("server");
	contextValue: PlcStatus = "tao-plc-off";

	declare parent: ProjectItem;

	ip!: string;
	public username?: string;
	public passwordHash?: string;
	trace?: TraceCfg;

	token?: string;
	plcMonitorUi?: PlcMonitorUi;
	plcTraceUi?: PlcTraceUi;
	plcLogUi?: PlcLogUi;
	taskPropertyUi?: TaskPropertyUi;
	variablesUi?: PlcVariablesUi;
	busManagerUI?: BusManagerUi;
	busNum: number = 0;
	socket: WebSock;
	rootPassword?: string;

	constructor(cfg: PlcCfg, parent: ProjectItem) {
		cfg.type = PlcItem.name;
		if (!cfg.label) cfg.label = cfg.ip;
		super(PlcItem.name, cfg.label, parent);
		this.busNum = cfg.busNum ?? 0;
		this.tooltip = cfg.ip;
		cfg.busNum = this.busNum;
		this.disposer.willDispose(() => {
			if (this.plcMonitorUi) this.plcMonitorUi.dispose();
			if (this.plcTraceUi) this.plcTraceUi.dispose();
			if (this.plcLogUi) this.plcLogUi.dispose();
			if (this.variablesUi) this.variablesUi.dispose();
			if (this.taskPropertyUi) this.taskPropertyUi.dispose();
			if (this.busManagerUI) this.busManagerUI.dispose();
			this.socket.close();
		});
		plcCfgKeys.forEach((key) => {
			if (key !== "children") (this[key] as unknown) = cfg[key]!;
			else {
				cfg.children?.forEach((child) => {
					if (child.type === TaskItem.name) {
						new TaskItem(child as unknown as TaskCfg, this);
					} else if (child.type === BusManagerItem.name) {
						new BusManagerItem(child as unknown as BusManagerCfg, this);
					} else if (child.type === CrrcRackItem.name) {
						new CrrcRackItem(child as unknown as CrrcRackCfg, this);
					} else if (child.type === CrrcModbusRtuMasterParams.name) {
						new CrrcModbusRtuMasterItem(
							child as unknown as CrrcModbusRtuMasterParams,
							this,
						);
					} else if (child.type === CrrcModbusRtuSlaveParams.name) {
						new CrrcModbusRtuSlaveItem(
							child as unknown as CrrcModbusRtuSlaveParams,
							this,
						);
					} else if (child.type === CrrcModbusTcpClientParams.name) {
						new CrrcModbusTcpClientItem(
							child as unknown as CrrcModbusTcpClientParams,
							this,
						);
					} else if (child.type === CrrcModbusTcpServerParams.name) {
						new CrrcModbusTcpServerItem(
							child as unknown as CrrcModbusTcpServerParams,
							this,
						);
					}
				});
			}
		});

		if (
			ide.pkg.taotech.showDeviceMenu &&
			!this.children.some(
				(child) =>
					child.type === CrrcRackItem.name && (child as CrrcRackItem).cfg.index === 0,
			)
		)
			new CrrcRackItem(new CrrcRackCfg(0), this);

		this.socket = jsonRpcSubscribe(
			apiUrl(this.ip, "device"),
			"Summary",
			{
				token: this.token,
				sampleInterval: 1000000000,
				publishInterval: 1000,
			},
			(result?: Summary) => {
				if (result) {
					const plc = this.parent.upsertPlc(this.ip, result.id);
					if (plc === this) void this.updateStatus(result);
				} else void this.updateStatus(undefined);
			},
		);
	}

	toJSON(): unknown {
		const cfg: PlcCfg = new PlcCfg();
		plcCfgKeys.forEach((key) => ((cfg[key] as unknown) = this[key]));
		return cfg;
	}

	async updateStatus(result?: Summary) {
		let savePlc = false;
		let refreshPlc;

		if (result) {
			refreshPlc = this.setStatus("tao-plc-on");
			if (this.label !== result.label) {
				this.label = result.label;
				savePlc = true;
			}
			if (this.id !== result.id) {
				this.id = result.id;
				savePlc = true;
			}
		} else {
			refreshPlc = this.setStatus("tao-plc-off");
		}

		const [saveTasks, refreshTasks] = this.updateTasksStatus(result?.tasks);

		if (savePlc || saveTasks) this.parent.save();
		if (savePlc || refreshPlc || saveTasks || refreshTasks) ide.solution.refresh(this);

		if (this.contextValue === "tao-plc-on" && refreshPlc && this.username && this.passwordHash)
			this.login();
	}

	setStatus(value: PlcStatus): boolean {
		if (value === this.contextValue) return false;
		switch (value) {
			case "tao-plc-off":
				this.setColor("icon.foreground");
				break;
			case "tao-plc-on":
				if (this.contextValue === "tao-plc-login") return false;
				this.setColor("charts.blue");
				break;
			case "tao-plc-login":
				this.setColor("charts.green");
				break;
			default:
				return false;
		}
		this.contextValue = value;
		return true;
	}

	updateTasksStatus(tasks?: Task[]) {
		if (!tasks) tasks = [];

		let save = false;
		let refresh = false;

		//add tasks only exist remotely
		tasks.forEach((task) => {
			if (
				!this.children.some(
					(child) => child.type === TaskItem.name && child.label === task.name,
				)
			) {
				const cfg = new TaskCfg();
				cfg.label = task.name;
				new TaskItem(cfg, this);
				save = true;
			}
		});

		for (let i = this.children.length - 1; i >= 0; i--) {
			const child = this.children[i] as TaskItem;
			if (child.type !== TaskItem.name) continue;

			const srcExist = ide.fileExist(
				vscode.Uri.file(`${this.parent.folder}/${child.label}/CMakeLists.txt`),
			);
			const remote = tasks.find((task) => child.label === task.name);

			if (remote) {
				if (remote.status === "Stop") refresh = child.setColor("charts.blue") || refresh;
				else if (remote.status === "Running" || remote.status === "Idle")
					refresh = child.setColor("charts.green") || refresh;
				else refresh = child.setColor("charts.red") || refresh;
				refresh = child.setBadge(srcExist ? "" : "R") || refresh;
			} else {
				refresh = child.setColor("charts.line") || refresh;
				if (srcExist) {
					refresh = child.setBadge("L") || refresh;
				} else {
					this.children.splice(i, 1);
					child.dispose();
					save = true;
				}
			}
		}
		return [save, refresh];
	}

	async login() {
		try {
			let username = this.username;
			if (!username) {
				username = await vscode.window.showInputBox({
					placeHolder: "Enter the user name",
				});
				if (!username) return;
			}

			const salt: string = await jsonRpcCall(
				apiUrl(this.ip, "user"),
				new JsonRpcRequest("getSalt", { username }),
			);

			let passwordHash = this.passwordHash;
			if (!passwordHash) {
				const password = await vscode.window.showInputBox({
					placeHolder: "Enter the password",
					password: true,
				});
				if (!password) return;

				passwordHash = Crypto.createHash("sha256").update(password).digest("hex");
			}

			let userhash = Crypto.createHash("sha256")
				.update(passwordHash + username)
				.digest("hex");
			userhash = Crypto.createHash("sha256")
				.update(userhash + salt)
				.digest("hex");

			try {
				this.token = await jsonRpcCall(
					apiUrl(this.ip, "user"),
					new JsonRpcRequest("login", { username, userhash }),
				);
			} catch {
				vscode.window.showErrorMessage("The password does not match the user name");
				return;
			}
			this.username = username;
			this.passwordHash = passwordHash;
			if (this.setStatus("tao-plc-login")) ide.solution.refresh(this);
			this.parent.save();
		} catch (e) {
			ide.logError(e);
		}
	}

	monitor() {
		if (!this.plcMonitorUi)
			this.plcMonitorUi = new PlcMonitorUi(
				this.ip,
				this.token!,
				() => (this.plcMonitorUi = undefined),
			);
		else this.plcMonitorUi.reveal();
	}

	showTrace() {
		if (!this.plcTraceUi)
			this.plcTraceUi = new PlcTraceUi(this, undefined, () => (this.plcTraceUi = undefined));
		else this.plcTraceUi.reveal();
	}

	log() {
		if (!this.plcLogUi)
			this.plcLogUi = new PlcLogUi(
				this.ip,
				this.id!,
				this.token!,
				undefined,
				() => (this.plcLogUi = undefined),
			);
		else this.plcLogUi.reveal();
	}

	variables() {
		if (!this.variablesUi)
			this.variablesUi = new PlcVariablesUi(
				this.ip,
				this.token!,
				undefined,
				() => (this.variablesUi = undefined),
			);
		else this.variablesUi.reveal();
	}

	async getRootPassword() {
		if (this.rootPassword === undefined) {
			this.rootPassword = await vscode.window.showInputBox({
				placeHolder: "Enter root password",
				password: true,
			});
		}
	}

	async getShell() {
		const shell = ide.onLinux() ? "sshpass" : ide.pathJoin(ide.appDir, "bin/klink.exe");
		const args = ide.onLinux() ? [] : ["-auto-store-sshkey", "-batch"];
		if (this.rootPassword) {
			args.push(ide.onLinux() ? "-p" : "-pw");
			args.push(this.rootPassword);
		}
		if (ide.onLinux()) args.push("ssh", "-o", "StrictHostKeyChecking=accept-new");
		args.push("root@" + this.ip);
		return { shell, args };
	}

	async debug() {
		try {
			if (vscode.debug.activeDebugSession) {
				vscode.commands.executeCommand("workbench.view.debug");
				return;
			}
			const cmakeProject = await ide.cmake?.getProject(this.parent.workspaceFolder.uri);
			const buildDir = await cmakeProject?.getBuildDirectory();

			await this.getRootPassword();
			if (this.rootPassword === undefined) return;
			const { shell: pipeProgram, args: pipeArgs } = await this.getShell();

			let stdout, stderr;
			try {
				({ stdout, stderr } = await Util.promisify(ChildProcess.execFile)(
					pipeProgram,
					pipeArgs.concat(["pidof", "NGPIDE"]),
				));
			} catch (error) {
				let err = error as { stdout: string; stderr: string };
				stdout = err.stdout;
				stderr = err.stderr;
			}
			if (stderr) throw new Error(stderr);
			let pid: number | undefined;
			if (stdout) {
				const pidStr = stdout.substring(0, stdout.length - 1);
				const pidTmp = Number(pidStr);
				if (pidTmp.toString() === pidStr && Number.isInteger(pidTmp)) pid = pidTmp;
				else throw new Error(stdout);
			}

			const cfg =
				pid !== undefined
					? {
							name: `${this.label}`,
							type: "cppdbg",
							request: "attach",
							logging: { engineLogging: true, trace: true, traceResponse: true },
							program: "/apps/runtime/NGPIDE",
							processId: pid,
							sourceFileMap: {
								[buildDir!]: {
									editorPath: " ",
									useForBreakpoints: false,
								},
							},
							pipeTransport: {
								debuggerPath: "gdb",
								pipeProgram,
								pipeArgs,
							},
							MIMode: "gdb",
							setupCommands: [
								{
									description: "Enable pretty-printing for gdb",
									text: "-enable-pretty-printing",
									ignoreFailures: true,
								},
								{
									description: "Pass signals",
									text: "handle SIG34 SIG40 pass noprint",
									ignoreFailures: true,
								},
							],
						}
					: {
							name: `${this.label}`,
							type: "cppdbg",
							request: "launch",
							logging: { engineLogging: true, trace: true, traceResponse: true },
							program: "/apps/runtime/NGPIDE",
							args: [],
							stopAtEntry: false,
							cwd: "/apps/runtime",
							sourceFileMap: {
								[buildDir!]: {
									editorPath: " ",
									useForBreakpoints: false,
								},
							},
							environment: [],
							externalConsole: false,
							pipeTransport: {
								debuggerPath: "gdb",
								pipeProgram,
								pipeArgs,
							},
							MIMode: "gdb",
							setupCommands: [
								{
									description: "Enable pretty-printing for gdb",
									text: "-enable-pretty-printing",
									ignoreFailures: true,
								},
								{
									description: "Pass signals",
									text: "handle SIG34 SIG40 pass noprint",
									ignoreFailures: true,
								},
							],
						};

			ide.writeJsonFile(ide.pathJoin(this.parent.folder, ".vscode/launch.json"), {
				version: "0.2.0",
				configurations: [cfg],
			}); //TODO: remove

			const result = await vscode.debug.startDebugging(this.parent.workspaceFolder, cfg);
			if (!result) throw new Error("Failed to start debugging session");
			await vscode.commands.executeCommand("workbench.view.debug");
		} catch (error) {
			this.rootPassword = undefined;
			vscode.window.showErrorMessage(ide.errorToString(error));
		}
	}

	async terminal() {
		await this.getRootPassword();
		if (this.rootPassword === undefined) return;
		const { shell, args } = await this.getShell();
		const terminal = vscode.window.createTerminal(this.label, shell, args);
		terminal.show();
	}

	async files() {
		await this.getRootPassword();
		if (this.rootPassword === undefined) return;
		ide.writeJsonFile(this.parent.folder + "/.vscode/sftp.json", {
			name: this.label,
			host: this.ip,
			username: "root",
			password: this.rootPassword,
			autoUpload: false,
			remotePath: "/",
		});
		await vscode.commands.executeCommand("sftp.config");
		await vscode.commands.executeCommand("workbench.view.extension.sftp");
		setTimeout(() => {
			void vscode.commands.executeCommand("workbench.action.closeActiveEditor");
		}, 100);
	}

	remove() {
		this.parent.removeChild(this.parent, this);
	}

	showAddTask() {
		if (!this.taskPropertyUi) {
			const allTasks: string[] = [];
			const addTasks: string[] = [];
			this.parent.children.forEach((projectChild) => {
				if (projectChild instanceof TaskSrcItem) {
					allTasks.push(projectChild.label);
					if (
						!this.children.some(
							(plcChild) =>
								plcChild instanceof TaskItem &&
								plcChild.label === projectChild.label,
						)
					)
						addTasks.push(projectChild.label);
				}
			});
			this.taskPropertyUi = new TaskPropertyUi(
				this.ip,
				this.token!,
				undefined,
				addTasks,
				allTasks,
				(params) => this.addTask(params),
				() => (this.taskPropertyUi = undefined),
			);
		} else this.taskPropertyUi.reveal();
	}

	addTask(params: TaskUpsertParams) {
		if (
			!this.parent.children.some(
				(child) => child instanceof TaskSrcItem && child.label === params.label,
			)
		) {
			upsertTaskSrc(this.parent, params, false);
			new TaskSrcItem(params, this.parent);
			upsertProjectSrcTasks(this.parent);
			ide.solution.refresh(this.parent);
		}
		new TaskItem(params, this);
		const state = this.collapsibleState;
		this.collapsibleState = vscode.TreeItemCollapsibleState.Collapsed;
		ide.solution.refresh(this);
		this.parent.save();
		return true;
	}

	showAddBus() {
		if (this.busNum > busNumMax) {
			void vscode.window.showErrorMessage("CANBus is maximum!");
			return;
		}

		if (!this.busManagerUI) {
			this.busManagerUI = new BusManagerUi(
				this,
				BUSEDITTYPE.BUSEDITTYPE_BUS,
				(params) => this.addBus(params as BusManagerCfg),
				() => (this.busManagerUI = undefined),
			);
		} else this.busManagerUI.reveal();
	}

	addBus(params: BusManagerCfg) {
		this.busNum++;
		new BusManagerItem(params, this);
		ide.solution.refresh(this);
		this.parent.save();
		canBusConfigFileSave();
		return true;
	}

	removeBus(busManagerItem: BusManagerItem) {
		this.children.splice(this.children.indexOf(busManagerItem), 1);
		let index = 0;
		this.children.forEach((projectChild) => {
			if (projectChild instanceof BusManagerItem) {
				projectChild.index = index;
				projectChild.label = index > 0 ? `CANBus_${index}` : "CANBus";
				index++;
			}
		});
		busManagerItem.dispose();
		this.busNum--;
		ide.solution.refresh(this);
		this.parent.save();
		canBusConfigFileSave();
	}

	async crrcAddRack() {
		const indexies = Array.from({ length: 9 }, (e, i) => i)
			.filter(
				(e, i) =>
					!this.children.some(
						(child) => child instanceof CrrcRackItem && child.cfg.index === i,
					),
			)
			.map((e) => String(e));
		const index = await vscode.window.showQuickPick(indexies, {
			canPickMany: false,
			placeHolder: "please select rack number:",
		});
		if (!index) return;
		new CrrcRackItem(new CrrcRackCfg(parseInt(index)), this);
		ide.solution.refresh(this);
		this.parent.save();
	}

	async crrcAddModbusRtuMaster() {
		const item = new CrrcModbusRtuMasterItem(new CrrcModbusRtuMasterParams(), this);
		ide.solution.refresh(this);
		this.parent.save();
		item.showEdit();
	}

	async crrcAddModbusRtuSlave() {
		const item = new CrrcModbusRtuSlaveItem(new CrrcModbusRtuSlaveParams(), this);
		ide.solution.refresh(this);
		this.parent.save();
		item.showEdit();
	}

	async crrcAddModbusTcpClient() {
		const item = new CrrcModbusTcpClientItem(new CrrcModbusTcpClientParams(), this);
		ide.solution.refresh(this);
		this.parent.save();
		item.showEdit();
	}

	async crrcAddModbusTcpServer() {
		const item = new CrrcModbusTcpServerItem(new CrrcModbusTcpServerParams(), this);
		ide.solution.refresh(this);
		this.parent.save();
		item.showEdit();
	}

	async crrcUpsertParams() {
		const params = this.children.filter(
			(child) =>
				child instanceof CrrcRackItem ||
				child instanceof CrrcModbusRtuMasterItem ||
				child instanceof CrrcModbusRtuSlaveItem ||
				child instanceof CrrcModbusTcpClientItem ||
				child instanceof CrrcModbusTcpServerItem,
		);
		jsonRpcCall<true, typeof params>(
			apiUrl(this.ip, "crrc"),
			new JsonRpcRequest(CrrcUpsertADReq.METHOD, params, this.token),
		)
			.then(() => {
				vscode.window.showInformationMessage("upsert success");
			})
			.catch((error: Error) => {
				vscode.window.showErrorMessage("upsert failed");
			});
	}
}
