import React, { useRef } from "react";
import { useState } from "react";
import {
	Select,
	Input,
	InputNumber,
	Divider,
	Form,
	Tabs,
	Table,
	Checkbox,
	Typography,
	Modal,
	Button,
	Notification,
	Image,
} from "@arco-design/web-react";
import { IconRobotAdd, IconDelete, IconEdit, IconUpload } from "@arco-design/web-react/icon";
import { vscodeWeb } from "@taotech/libweb";
import { apiUrl, jsonRpcCall, JsonRpcRequest, parseJson } from "@taotech/libdual";
import "./App.css";
import { CrrcUpsertCANReq, CrrcRacksCANsParams, CrrcCanPdoParams } from "./msg.ts";

const style = {
	textAlign: "center",
	marginTop: 20,
};
const root = document.getElementById("root")!;
const ip = root.dataset.ip!;
const token = root.dataset.token!;
const productUrl = root.dataset.productUrl!;
const indexies = parseJson<string[]>(root.dataset.indexies);
// 行数据的类型包含了 key、name、object 等属性
type CanConfigType = {
	id?: string;
	direction?: string;
	channel?: string;
	type?: string;
	type2?: string;
	len?: string;
	trigger?: string;
	cycle?: string;
	remark?: string;
	key: string;
};
class CrrcCanCfg {
	can1Enabled: boolean = true;
	can2Enabled: boolean = false;
	can1Baudrate: string = "500Kbps";
	can2Baudrate: string = "500Kbps";
	rackId?: string = "0";
	nodeId?: string = "1";
	canConfigType: CanConfigType[] = [];
}

const crrcCanEditDefaulValue: CanConfigType = {
	id: "",
	direction: "输入",
	channel: "CAN1",
	type: "标准帧",
	type2: "数据帧",
	len: "8",
	trigger: "周期",
	cycle: "10",
	remark: "",
	key: "",
};

interface GetStatusParam {
	rack: number;
	node: number;
}

interface GetStatusResult {
	type: number;
	version: number;
	status: number;
}

const StatusMap = ["不在线", "在线"];

export default function App() {
	const options = [
		"1Mbps",
		"800Kbps",
		"500Kbps",
		"250Kbps",
		"125Kbps",
		"100Kbps",
		"50Kbps",
		"20Kbps",
		"10Kbps",
	];
	const lenOptions = [
		"0",
		"1",
		"2",
		"3",
		"4",
		"5",
		"6",
		"7",
		"8",
	];
	const directionOptions = ["输入", "输出"];
	const channelOptions = ["CAN1", "CAN2"];
	const triggerOptions = ["周期", "触发"];
	const typeOptions = ["标准帧", "扩展帧"];
	const type2Options = ["数据帧", "远程帧"];
	const editCanConfigColumns = [
		{
			title: "帧ID(0x)",
			dataIndex: "id",
		},
		{
			title: "输入/输出",
			dataIndex: "direction",
		},
		{
			title: "CAN通道",
			dataIndex: "channel",
		},
		{
			title: "帧类型",
			dataIndex: "type",
		},
		{
			title: "帧格式",
			dataIndex: "type2",
		},
		{
			title: "数据长度",
			dataIndex: "len",
		},
		{
			title: "触发模式",
			dataIndex: "trigger",
		},
		{
			title: "周期(ms)",
			dataIndex: "cycle",
		},
		{
			title: "备注",
			dataIndex: "remark",
		},
	];

	//can主设备设置交互消息类型定义
	interface CrrcCanMessage {
		command: string;
		params: CrrcRacksCANsParams;
	}

	interface CrrcCanMessageEvent {
		data: CrrcCanMessage;
	}

	const statusTimer = useRef<ReturnType<typeof setInterval>>();
	const [moduleType, setModuleType] = useState("正在获取...");
	const [moduleVersion, setModuleVersion] = useState("正在获取...");
	const [moduleStatus, setModuleStatus] = useState("正在获取...");

	const [statusGetOk, setStatusGetOk] = useState(false);

	function onTabChange(key: string) {
		if (key === "status" && !statusGetOk) {
			statusTimer.current = setInterval(() => {
				jsonRpcCall<GetStatusResult, GetStatusParam>(
					apiUrl(ip, "crrc"),
					new JsonRpcRequest(
						"getStatus",
						{ rack: Params[0].index, node: parseInt(InterData.nodeId!) },
						token,
					),
				)
					.then((result: GetStatusResult) => {
						setModuleType(String(result.type));
						setModuleVersion(String(result.version));
						setModuleStatus(StatusMap[result.status]);
						setStatusGetOk(true);
					})
					.catch((reason) => {
						setModuleType("获取失败");
						setModuleVersion("获取失败");
						setModuleStatus("获取失败");
						console.error(reason);
					});
			}, 1000);
		} else {
			if (!statusGetOk) {
				setModuleType("正在获取...");
				setModuleVersion("正在获取...");
				setModuleStatus("正在获取...");
			}
			clearInterval(statusTimer.current);
		}
	}

	function baudrateParseNumber(baudrate: string): number {
		let numberBaudrate = 0;
		switch (baudrate) {
			case "1Mbps":
				numberBaudrate = 0;
				break;
			case "800Kbps":
				numberBaudrate = 1;
				break;
			case "500Kbps":
				numberBaudrate = 2;
				break;
			case "250Kbps":
				numberBaudrate = 3;
				break;
			case "125Kbps":
				numberBaudrate = 4;
				break;
			case "100Kbps":
				numberBaudrate = 5;
				break;
			case "50Kbps":
				numberBaudrate = 6;
				break;
			case "20Kbps":
				numberBaudrate = 7;
				break;
			case "10Kbps":
				numberBaudrate = 8;
				break;
		}

		return numberBaudrate;
	}

	function baudrateParseString(baudrate: number): string {
		let baudrateStr = "500Kbps";
		switch (baudrate) {
			case 0:
				baudrateStr = "1Mbps";
				break;
			case 1:
				baudrateStr = "800Kbps";
				break;
			case 2:
				baudrateStr = "500Kbps";
				break;
			case 3:
				baudrateStr = "250Kbps";
				break;
			case 4:
				baudrateStr = "125Kbps";
				break;
			case 5:
				baudrateStr = "100Kbps";
				break;
			case 6:
				baudrateStr = "50Kbps";
				break;
			case 7:
				baudrateStr = "20Kbps";
				break;
			case 8:
				baudrateStr = "10Kbps";
				break;
		}

		return baudrateStr;
	}

	function canEnabledParseNumber(can1: boolean, can2: boolean): number {
		let numberType = 0;
		if (can1 && can2) {
			numberType = 2;
		} else if (can1) {
			numberType = 0;
		} else if (can2) {
			numberType = 1;
		}

		return numberType;
	}

	function canEnabledParseBool(enable: number): [boolean, boolean] {
		switch (enable) {
			case 1:
				return [false, true];
			case 2:
				return [true, true];
			case 0:
			default:
				return [true, false];
		}
	}
	function canChannelParseNumber(channel: string): number {
		let numberType = 0;
		if (channel === "CAN2") {
			numberType = 1;
		}
		return numberType;
	}

	function canChannelParseString(channel: number): string {
		let channelStr = "CAN1";
		if (channel === 1) {
			channelStr = "CAN2";
		}
		return channelStr;
	}

	function canType1ParseNumber(type: string): number {
		let numberType = 0;
		if (type === "扩展帧") {
			numberType = 1;
		}
		return numberType;
	}

	function canType1ParseString(type: number): string {
		let typeStr = "标准帧";
		if (type === 1) {
			typeStr = "扩展帧";
		}
		return typeStr;
	}
	function canType2ParseNumber(type: string): number {
		let numberType = 0;
		if (type === "远程帧") {
			numberType = 1;
		}
		return numberType;
	}
	function canType2ParseString(type: number): string {
		let typeStr = "数据帧";
		if (type === 1) {
			typeStr = "远程帧";
		}
		return typeStr;
	}

	function canDirectionParseNumber(direction: string): number {
		let type = 0;
		if (direction === "输出") {
			type = 1;
		}
		return type;
	}

	function canDirectionParseString(direction: number): string {
		let directionStr = "输入";
		if (direction === 1) {
			directionStr = "输出";
		}
		return directionStr;
	}

	function canTriggerParseNumber(trigger: string): number {
		let type = 0;
		if (trigger === "触发") {
			type = 1;
		}
		return type;
	}
	function canTriggerParseString(trigger: number): string {
		let triggerStr = "周期";
		if (trigger === 1) {
			triggerStr = "触发";
		}
		return triggerStr;
	}

	const crrcCanDataDefaulValue: CrrcCanCfg = {
		can1Enabled: true,
		can2Enabled: false,
		can1Baudrate: "500Kbps",
		can2Baudrate: "500Kbps",
		rackId: "0",
		nodeId: "1",
		canConfigType: [],
	};
	let interData: CrrcCanCfg = crrcCanDataDefaulValue;
	const [InterData, setInterData] = useState(interData);

	window.addEventListener("message", (event: CrrcCanMessageEvent) => {
		if (event.data.command === "InitData") {
			params = {...event.data.params};
			// 初始化数据
			interData = { ...InterData };
			interData.canConfigType = [];
			[interData.can1Enabled, interData.can2Enabled] = canEnabledParseBool(
				params[0].children[0].enableType,
			);
			interData.can1Baudrate = baudrateParseString(params[0].children[0].can1Baudrate);
			interData.can2Baudrate = baudrateParseString(params[0].children[0].can2Baudrate);
			interData.nodeId = params[0].children[0].index.toString();
			if (params[0].children[0].pdos) {
				for (let i = 0; i < params![0].children[0].pdos.length; i++) {
					const pdo = params![0].children[0].pdos[i];
					const canConfigType: CanConfigType = {
						id: pdo.id.toString(16),
						direction: canDirectionParseString(pdo.direction),
						channel: canChannelParseString(pdo.channel),
						type: canType1ParseString(pdo.type),
						type2: canType2ParseString(pdo.type2),
						len: pdo.len.toString(),
						trigger: canTriggerParseString(pdo.trigger),
						cycle: pdo.cycle.toString(),
						remark: pdo.remark,
						key: pdo.id.toString(),
					};
					interData.canConfigType.push(canConfigType);
				}
			}
			setInterData(interData);
			setParams(params);
		}
	});

	function submit(values: CrrcRacksCANsParams) {
		const req = new CrrcUpsertCANReq([{ index: values[0].index, children: [...values[0].children]}], token);
		vscodeWeb.postMessage(req);

		jsonRpcCall<true, CrrcRacksCANsParams>(apiUrl(ip, "crrc"), req)
			.then(() => {
				vscodeWeb.close();
			})
			.catch((reason) => {
				console.error(reason);
				Notification.error({
					content: "设置失败，请确认设备在线后重试",
				});
			});
	}

	let params: CrrcRacksCANsParams = {} as CrrcRacksCANsParams;
	const [Params, setParams] = useState(params);
	const [PdoPropertyVisble, setPdoPropertyVisble] = useState(false);
	let canPdoEditData: CanConfigType = {} as CanConfigType;
	const [CanPdoEditData, setCanPdoEditData] = useState(canPdoEditData);
	const [EditCanSelectedKeys, setEditCanSelectedKeys] = useState<string[]>([""]);
	const [Pdoedit, setPdoedit] = useState(false);
	let editPoRowSelctedKey = "";
	const [EditPoRowSelctedKey, setEditPoRowSelctedKey] = useState(editPoRowSelctedKey);
	const [LenSelectDisabled, setLenSelectDisabled] = useState(false);
	// 输入框 handleInputChange
	const handleInputChange = (value: string, e: React.ChangeEvent<HTMLInputElement>) => {
		canPdoEditData = { ...CanPdoEditData };

		switch (e.target.id) {
			case "CanPdoEditData.id":
				canPdoEditData.id = value;
				break;
			case "CanPdoEditData.remark":
				canPdoEditData.remark = value;
				break;
			default:
				return;
		}

		setCanPdoEditData(canPdoEditData);
	};

	// 输入框 handleInputChange
	const handleInputCycleChange = (value: number) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.cycle = value.toString(10);
		setCanPdoEditData(canPdoEditData);
	};

	const handleEditCanRowClick = (record: CanConfigType, index: number) => {
		editPoRowSelctedKey = record.key;
		setEditPoRowSelctedKey(editPoRowSelctedKey);
	};

	const handleSelectCAN1BaudrateChange = (value: string) => {
		interData = { ...InterData };
		interData.can1Baudrate = value;
		setInterData(interData);
		params = { ...Params };
		params[0].children[0].can1Baudrate = baudrateParseNumber(value);
		setParams(params);
	};

	const handleSelectCAN2BaudrateChange = (value: string) => {
		interData = { ...InterData };
		interData.can2Baudrate = value;
		setInterData(interData);
		params = { ...Params };
		params[0].children[0].can2Baudrate = baudrateParseNumber(value);
		setParams(params);
	};

	const handleSelectNodeIdChange = (value: string) => {
		interData = { ...InterData };
		interData.nodeId = value;
		setInterData(interData);
		params = { ...Params };
		params[0].children[0].index = parseInt(value, 10);
		setParams(params);
	};
	const handleAutoStartMangerCheckboxChange = (e: boolean) => {
		interData = { ...InterData };
		interData.can1Enabled = e;
		setInterData(interData);
		params = { ...Params };
		params[0].children[0].enableType = canEnabledParseNumber(
			interData.can1Enabled,
			interData.can2Enabled,
		);
		setParams(params);
	};

	const handleSlaveEnablePollingCheckboxChange = (e: boolean) => {
		interData = { ...InterData };
		interData.can2Enabled = e;
		setInterData(interData);
		params = { ...Params };
		params[0].children[0].enableType = canEnabledParseNumber(
			interData.can1Enabled,
			interData.can2Enabled,
		);
		setParams(params);
	};

	const handleSelectDirectionChange = (value: string) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.direction = value;
		setCanPdoEditData(canPdoEditData);
	};
	const handleSelectChannelChange = (value: string) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.channel = value;
		setCanPdoEditData(canPdoEditData);
	};
	const handleSelectTypeChange = (value: string) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.type = value;
		setCanPdoEditData(canPdoEditData);
	};
	const handleSelectLenChange = (value: string) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.len = value;
		setCanPdoEditData(canPdoEditData);
	};
	const handleSelectType1Change = (value: string) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.type2 = value;
		if (canPdoEditData.type2 === "远程帧") {
			canPdoEditData.len = "0";
			setLenSelectDisabled(true);
		} else {
			canPdoEditData.len = "8";
			setLenSelectDisabled(false);
		}
		setCanPdoEditData(canPdoEditData);
	};
	const handleSelectTriggerChange = (value: string) => {
		canPdoEditData = { ...CanPdoEditData };
		canPdoEditData.trigger = value;
		setCanPdoEditData(canPdoEditData);
	};
	const handlePdoPropertyOk = () => {
		setPdoPropertyVisble(false);
		optionPdoDatapdate(CanPdoEditData, Pdoedit);
	};

	const handlePdoPropertyCancel = () => {
		setPdoPropertyVisble(false);
	};

	const handleAddPdo = () => {
		setLenSelectDisabled(false);
		setCanPdoEditData(crrcCanEditDefaulValue);
		setPdoPropertyVisble(true);
		setPdoedit(false);
	};

	const handleDeletePdo = () => {
		if (EditPoRowSelctedKey === "") {
			return;
		}
		deletePdoDatapdate(EditPoRowSelctedKey);
	};

	const handleEditPdo = () => {
		if (EditPoRowSelctedKey === "") {
			return;
		}

		editPdoDatapdate(EditPoRowSelctedKey);
	};

	const handleComfirmPdo = () => {
		submit(Params);
	};

	function deletePdoDatapdate(key: string) {
		const pdoData: CanConfigType[] = [...InterData.canConfigType];
		interData = { ...InterData };

		for (let i = 0; i < pdoData.length; i++) {
			if (pdoData[i].key === key) {
				pdoData.splice(i, 1);
				interData.canConfigType = [...pdoData];
				setInterData(interData);
				params = { ...Params };
				params[0].children[0].pdos.splice(i, 1);
				setParams(params);
				return;
			}
		}
	}

	function editPdoDatapdate(key: string) {
		const pdoData: CanConfigType[] = [...InterData.canConfigType];

		for (let i = 0; i < pdoData.length; i++) {
			if (pdoData[i].key === key) {
				setCanPdoEditData(pdoData[i]);
				if (pdoData[i].type2 === "远程帧") {
					setLenSelectDisabled(true);
				}
				setPdoPropertyVisble(true);
				setPdoedit(true);
				return;
			}
		}
	}
	function optionPdoDatapdate(pdo: CanConfigType, edit: boolean) {
		const pdoData: CanConfigType[] = [...InterData.canConfigType];

		if (pdo.id === "" || pdo.id === undefined) {
			Modal.error({
				title: "错误",
				content: "帧ID不能为空！",
			});
			return;
		}
		if (pdo.cycle === "" || pdo.cycle === undefined) {
			Modal.error({
				title: "错误",
				content: "周期不能为空！",
			});
			return;
		}
		if (isNaN(parseInt(pdo.cycle, 10))) {
			Modal.error({
				title: "错误",
				content: "周期必须为数字！",
			});
			return;
		}
		if (parseInt(pdo.cycle, 10) < 0 || parseInt(pdo.cycle, 10) > 65535) {
			Modal.error({
				title: "错误",
				content: "周期必须在0~65535之间！",
			});
			return;
		}
		if (
			parseInt(pdo.len!, 10) > 8 ||
			parseInt(pdo.len!, 10) < 0 ||
			(pdo.type2 === "远程帧" && parseInt(pdo.len!, 10) !== 0)
		) {
			Modal.error({
				title: "错误",
				content: "帧长度错误，请重新输入！",
			});
			return;
		}

		interData = { ...InterData };

		if (edit) {
			for (let i = 0; i < pdoData.length; i++) {
				if (pdoData[i].key === pdo.key) {
					pdoData[i] = { ...pdo };
					interData.canConfigType = [...pdoData];
					setInterData(interData);
					params = { ...Params };
					params[0].children[0].pdos[i] = {
						id: parseInt(pdo.id, 16),
						direction: canDirectionParseNumber(pdo.direction!),
						channel: canChannelParseNumber(pdo.channel!),
						type: canType1ParseNumber(pdo.type!),
						type2: canType2ParseNumber(pdo.type2!),
						len: parseInt(pdo.len!, 10),
						trigger: canTriggerParseNumber(pdo.trigger!),
						cycle: parseInt(pdo.cycle, 10),
						remark: pdo.remark ? pdo.remark : "",
						key: pdo.key,
					};
					setParams(params);
					return;
				}
			}
		} else {
			pdo.key = `${Date.now()}${Math.floor(Math.random() * 1000000)}`;
			pdoData.push({ ...pdo });
			interData.canConfigType = [...pdoData];
			setInterData(interData);
			params = { ...Params };
			const PdoParams: CrrcCanPdoParams = {
				id: parseInt(pdo.id, 16),
				direction: canDirectionParseNumber(pdo.direction!),
				channel: canChannelParseNumber(pdo.channel!),
				type: canType1ParseNumber(pdo.type!),
				type2: canType2ParseNumber(pdo.type2!),
				len: parseInt(pdo.len!, 10),
				trigger: canTriggerParseNumber(pdo.trigger!),
				cycle: parseInt(pdo.cycle, 10),
				remark: pdo.remark ? pdo.remark : "",
				key: pdo.key,
			};
			params[0].children[0].pdos.push(PdoParams);
			setParams(params);
		}
	}

	const handleKeyDown = (e: React.KeyboardEvent) => {
		const input = e.key.toLowerCase();
		const validChars = "0123456789abcdef";

		if (
			!validChars.includes(input) &&
			input !== "delete" &&
			input !== "backspace" &&
			input !== "arrowleft" &&
			input !== "arrowright"
		) {
			e.preventDefault();
		}
	};

	return (
		<div>
			<Tabs tabPosition="left" className="m-4" onChange={onTabChange}>
				<Tabs.TabPane key="config" title="通用">
					<Typography.Paragraph style={style as React.CSSProperties}>
						<Divider orientation="left">通用</Divider>
						<div style={{ display: "flex", textAlign: "left", paddingLeft: 35 }}>
							<div style={{ marginLeft: 150 }}>
								<Select
									addBefore="节点编号"
									id="nodeId"
									style={{ width: 400, textAlign: "left", paddingLeft: 5 }}
									allowClear
									value={InterData.nodeId}
									onChange={handleSelectNodeIdChange}
								>
									{indexies!.map((option) => (
										<Select.Option key={option} value={option}>
											{option}
										</Select.Option>
									))}
								</Select>
							</div>
						</div>
						<div style={{ display: "flex", textAlign: "left", paddingLeft: 35 }}>
							<Checkbox
								onChange={handleAutoStartMangerCheckboxChange}
								checked={InterData.can1Enabled}
								id="autoStartManger"
							>
								CAN1
							</Checkbox>
							<Checkbox
								id="slaveEnablePolling"
								style={{ width: 400, textAlign: "left", paddingLeft: 20 }}
								onChange={handleSlaveEnablePollingCheckboxChange}
								checked={InterData.can2Enabled}
							>
								CAN2
							</Checkbox>
						</div>
						<div style={{ display: "flex", textAlign: "left", paddingLeft: 35 }}>
							<div>
								<Select
									addBefore="CAN1波特率"
									id="idCan1Baudrate"
									style={{ width: 400, textAlign: "left", paddingLeft: 5 }}
									allowClear
									value={InterData.can1Baudrate}
									onChange={handleSelectCAN1BaudrateChange}
								>
									{options.map((option) => (
										<Select.Option key={option} value={option}>
											{option}
										</Select.Option>
									))}
								</Select>
							</div>
							<div style={{ marginLeft: 150 }}>
								<Select
									addBefore="CAN2波特率"
									id="idCan2Baudrate"
									style={{ width: 400, textAlign: "left", paddingLeft: 5 }}
									allowClear
									value={InterData.can2Baudrate}
									onChange={handleSelectCAN2BaudrateChange}
								>
									{options.map((option) => (
										<Select.Option key={option} value={option}>
											{option}
										</Select.Option>
									))}
								</Select>
							</div>
						</div>
						<div>
							<Table
								virtualized={true}
								scroll={{
									y: 300,
								}}
								border
								style={{ maxHeight: "400px" }} // 使用 style 属性设置最小宽度为 800px
								rowSelection={{
									type: "checkbox",
									onChange: (
										selectedRowKeys: (string | number)[],
										selectedRows: CanConfigType[],
									) => {
										// 处理选定行的变化
										setEditCanSelectedKeys(selectedRowKeys as string[]);
									},
								}}
								pagination={false}
								columns={editCanConfigColumns}
								data={InterData.canConfigType}
								onRow={(record, index) => {
									return {
										onClick: (event) => handleEditCanRowClick(record, index),
									};
								}}
							/>
							<Button.Group>
								<Button
									type="secondary"
									icon={<IconRobotAdd />}
									onClick={() => handleAddPdo()}
								>
									添加
								</Button>
								<Button
									type="secondary"
									icon={<IconDelete />}
									onClick={() => handleDeletePdo()}
								>
									删除
								</Button>
								<Button
									type="secondary"
									icon={<IconEdit />}
									onClick={() => handleEditPdo()}
								>
									编辑
								</Button>
							</Button.Group>
							<div>
								<Button
									type="primary"
									icon={<IconUpload />}
									onClick={() => handleComfirmPdo()}
								>
									确定
								</Button>
							</div>
						</div>
						<div>
							<Modal
								title="添加"
								visible={PdoPropertyVisble}
								onOk={handlePdoPropertyOk}
								onCancel={handlePdoPropertyCancel}
								autoFocus={false}
								focusLock={true}
								className="modal-demo-without-content-spacing"
								style={{ width: "800px" }}
							>
								<div
									style={{
										display: "flex",
										flexDirection: "column",
										alignItems: "flex-start",
										marginLeft: 20,
									}}
								>
									<Form.Item label="帧ID(0x):">
										<Input
											id="CanPdoEditData.id"
											onKeyDown={handleKeyDown}
											onChange={handleInputChange}
											value={CanPdoEditData.id}
											style={{ width: 200 }}
										/>
									</Form.Item>

									<Form.Item label="输入/输出:">
										<Select
											id="direction"
											style={{
												width: 400,
												textAlign: "left",
												paddingLeft: 5,
											}}
											allowClear
											value={CanPdoEditData.direction}
											onChange={handleSelectDirectionChange}
										>
											{directionOptions.map((option) => (
												<Select.Option key={option} value={option}>
													{option}
												</Select.Option>
											))}
										</Select>
									</Form.Item>

									<Form.Item label="CAN通道:">
										<Select
											id="channel" // 修改id为channel以避免重复
											style={{
												width: 400,
												textAlign: "left",
												paddingLeft: 5,
											}}
											allowClear
											value={CanPdoEditData.channel}
											onChange={handleSelectChannelChange}
										>
											{channelOptions.map((option) => (
												<Select.Option key={option} value={option}>
													{option}
												</Select.Option>
											))}
										</Select>
									</Form.Item>

									<Form.Item label="帧类型:">
										<Select
											id="type" // 修改id为type以避免重复
											style={{
												width: 400,
												textAlign: "left",
												paddingLeft: 5,
											}}
											allowClear
											value={CanPdoEditData.type}
											onChange={handleSelectTypeChange}
										>
											{typeOptions.map((option) => (
												<Select.Option key={option} value={option}>
													{option}
												</Select.Option>
											))}
										</Select>
									</Form.Item>

									<Form.Item label="帧格式:">
										<Select
											id="type2" // 修改id为type以避免重复
											style={{
												width: 400,
												textAlign: "left",
												paddingLeft: 5,
											}}
											allowClear
											value={CanPdoEditData.type2}
											onChange={handleSelectType1Change}
										>
											{type2Options.map((option) => (
												<Select.Option key={option} value={option}>
													{option}
												</Select.Option>
											))}
										</Select>
									</Form.Item>

									<Form.Item label="数据长度:">
									<Select
											id="CanPdoEditData.len" // 修改id为type以避免重复
											style={{
												width: 400,
												textAlign: "left",
												paddingLeft: 5,
											}}
											allowClear
											value={CanPdoEditData.len}
											onChange={handleSelectLenChange}
											disabled={LenSelectDisabled}
										>
											{lenOptions.map((option) => (
												<Select.Option key={option} value={option}>
													{option}
												</Select.Option>
											))}
										</Select>
									</Form.Item>
									<Form.Item label="触发模式:">
										<Select
											id="trigger" // 修改id为trigger以避免重复
											style={{
												width: 400,
												textAlign: "left",
												paddingLeft: 5,
											}}
											allowClear
											value={CanPdoEditData.trigger}
											onChange={handleSelectTriggerChange}
										>
											{triggerOptions.map((option) => (
												<Select.Option key={option} value={option}>
													{option}
												</Select.Option>
											))}
										</Select>
									</Form.Item>

									<Form.Item label="周期(ms):">
										<InputNumber
											id="CanPdoEditData.cycle"
											onChange={handleInputCycleChange}
											value={CanPdoEditData.cycle}
											style={{ width: 200 }}
										/>
									</Form.Item>

									<Form.Item label="备注:">
										<Input
											id="CanPdoEditData.remark"
											onChange={handleInputChange}
											value={CanPdoEditData.remark}
											style={{ width: 200 }}
										/>
									</Form.Item>
								</div>
							</Modal>
						</div>
					</Typography.Paragraph>
				</Tabs.TabPane>
				<Tabs.TabPane key="status" title="状态">
					<div>类型：{moduleType}</div>
					<div>版本号：{moduleVersion}</div>
					<div>状态：{moduleStatus}</div>
			   </Tabs.TabPane>
				<Tabs.TabPane key="info" title="信息" className="flex gap-8">
					{/* prettier-ignore */}
					<div>
型号：G1-2CAN<br/>
<br/>
<u>接口性能</u><br/>
链路协议：CAN2.0A/B<br/>
通道数量：×2<br/>
通讯速率：1Mbps Max（10米线缆），波特率可配置<br/>
应用协议：支持用户应用自由协议及CANOpen<br/>
隔离能力：与外部信号电源隔离，绝缘能力AC750V 1min<br/>
<br/>
<u>环境适应性能</u><br/>
温度：运行温度-40℃~70℃，存储温度-40℃~85℃<br/>
湿度：工作、存储湿度5%～95%<br/>
防护：防护等级IP20<br/>
				</div>
					<Image src={productUrl} />
				</Tabs.TabPane>
			</Tabs>
		</div>
	);
}
