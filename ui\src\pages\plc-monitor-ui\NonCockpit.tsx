import { useState, useEffect, useRef } from "react";

import { Typography, Table, TableColumnProps } from "@arco-design/web-react";
const { Title } = Typography;

import MonitorChart from "./MonitorChart";
import MemoryChart from "./MemoryChart";
import type EChartsReact from "echarts-for-react";

import { vscodeWeb } from "@taotech/libweb";

import type { FileSystem, Summary } from "@/msg";

function getThemeName(dark: boolean) {
	return dark ? "dark" : "";
}

const fileSystemColumns: TableColumnProps[] = [
	{
		title: "设备",
		dataIndex: "device",
	},
	{
		title: "挂载点",
		dataIndex: "directory",
	},
	{
		title: "文件系统类型",
		dataIndex: "type",
	},
	{
		title: "总容量(KB)",
		dataIndex: "totalSpace",
	},
	{
		title: "已使用(KB)",
		dataIndex: "usedSpace",
	},
];

export default function NonCockpit({ summary }: { summary?: Summary }) {
	const CHART_SECONDS = 60;

	const [chartTheme, setChartTheme] = useState(getThemeName(vscodeWeb.colorSchemeIsDark()));

	const [cpuUpTime, setCpuUpTime] = useState<number>(0);
	const [cpuUsage, setCpuUsage] = useState<number>(0);
	const cpuUsages = useRef<number[]>(Array<number>(CHART_SECONDS).fill(0));
	const cpuChartRef = useRef<EChartsReact>(null);

	const [memoryTotal, setMemoryTotal] = useState(0);
	const [memoryUsage, setMemoryUsage] = useState<number>(0);
	const memoryUsages = useRef<number[]>(Array<number>(CHART_SECONDS).fill(0));
	const memoryChartRef = useRef<EChartsReact>(null);

	const [fileSystems, setFileSystems] = useState<FileSystem[]>([]);

	useEffect(() => {
		vscodeWeb.colorSchemeListen((dark) => setChartTheme(getThemeName(dark)));
	}, []);

	useEffect(() => {
		if (!summary) return;
		if (summary.cpu) {
			setCpuUpTime(summary.cpu.uptime);
			if (summary.cpu.usage.length) {
				setCpuUsage(summary.cpu.usage[summary.cpu.usage.length - 1]);
				cpuUsages.current = cpuUsages.current.concat(summary.cpu.usage);
				if (cpuUsages.current.length > CHART_SECONDS)
					cpuUsages.current = cpuUsages.current.splice(
						cpuUsages.current.length - CHART_SECONDS,
					);
				cpuChartRef.current?.getEchartsInstance().setOption({
					series: [
						{
							data: cpuUsages.current,
						},
					],
				});
			}
		}
		if (summary.memory) {
			setMemoryTotal(summary.memory.total);
			if (summary.memory.usage.length) {
				setMemoryUsage(
					Math.round(
						(summary.memory.usage[summary.memory.usage.length - 1] /
							summary.memory.total) *
							100,
					),
				);
				memoryUsages.current = memoryUsages.current.concat(summary.memory.usage);
				if (memoryUsages.current.length > CHART_SECONDS)
					memoryUsages.current = memoryUsages.current.splice(
						memoryUsages.current.length - CHART_SECONDS,
					);
				memoryChartRef.current?.getEchartsInstance().setOption({
					series: [
						{
							data: memoryUsages.current,
						},
					],
				});
			}
		}
	}, [summary]);

	return (
		<>
			<div className="flex flex-row items-baseline">
				<Title heading={4} type="primary" className="basis-2/12">
					CPU
				</Title>
				<div className="basis-2/12">{cpuUsage}%</div>
				<div className="basis-8/12">总运行时长: {cpuUpTime}</div>
			</div>
			<MonitorChart
				ref={cpuChartRef}
				className="ml-8"
				theme={chartTheme}
				style={{ height: "160px", width: "100%" }}
				option={{
					animation: false,
					xAxis: {
						name: `${CHART_SECONDS}秒`,
						type: "category",
						boundaryGap: false,
						min: 0,
						max: CHART_SECONDS - 1,
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					yAxis: {
						name: "% 使用率",
						type: "value",
						axisLine: {
							show: true,
						},
						splitLine: {
							show: false,
						},
					},
					series: [
						{
							type: "line",
							areaStyle: {},
							smooth: true,
							showSymbol: false,
							lineStyle: {
								width: 0,
							},
						},
					],
					grid: {
						left: 64,
						top: 32,
						bottom: 8,
						right: 80,
					},
				}}
			/>

			<div className="flex flex-row items-baseline">
				<Title heading={4} type="primary" className="basis-2/12">
					内存
				</Title>
				<div className="basis-2/12">{memoryUsage}%</div>
				<div className="basis-8/12">总内存：{memoryTotal} KB</div>
			</div>
			<MemoryChart
				ref={memoryChartRef}
				className="ml-8"
				theme={chartTheme}
				style={{ height: "160px", width: "100%" }}
				option={{
					animation: false,
					xAxis: {
						name: `${CHART_SECONDS}秒`,
						type: "category",
						boundaryGap: false,
						min: 0,
						max: CHART_SECONDS - 1,
						axisTick: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					yAxis: {
						name: "KB",
						type: "value",
						axisLine: {
							show: true,
						},
						splitLine: {
							show: false,
						},
					},
					series: [
						{
							type: "line",
							areaStyle: {},
							smooth: true,
							showSymbol: false,
							lineStyle: {
								width: 0,
							},
						},
					],
					grid: {
						left: 64,
						top: 32,
						bottom: 8,
						right: 80,
					},
				}}
			/>

			<Title heading={4} type="primary">
				文件系统
			</Title>
			<Table
				columns={fileSystemColumns}
				data={fileSystems}
				rowKey="device"
				pagination={{ hideOnSinglePage: true }}
				className="ml-8"
			/>
		</>
	);
}
