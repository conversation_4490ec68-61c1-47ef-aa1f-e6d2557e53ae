import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import BusManagerItem from "./bus-manager-item";
import { CanBusMessage, crccCanSupport } from "./bus-type";
import { canBusConfigFileSave } from "./can-slave-manager-ui";

export default class CanBusManagerUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;
	busManagerItem: BusManagerItem;
	constructor(busManagerItem: BusManagerItem, ...disposes: Disposes) {
		super(...disposes);

		this.busManagerItem = busManagerItem;
		this.panel = vscode.window.createWebviewPanel(
			"CANBUS",
			"CAN总线设置",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);

		this.willDispose(this.panel);

		void this.panel.webview.postMessage({
			command: "CanBusMessage",
			data: {
				canPort: "" + this.busManagerItem.canPort,
				baudrate: "" + this.busManagerItem.baudrate,
				crccCan: crccCanSupport,
				canType: this.busManagerItem.canType,
				rackNo: this.busManagerItem.rackNo,
				nodeId: this.busManagerItem.nodeId,
			},
		});

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(message: CanBusMessage) => {
				switch (message.command) {
					case "CanBusMessage":
						this.busManagerItem.canPort = parseInt(message.data.canPort);
						this.busManagerItem.baudrate = parseInt(message.data.baudrate);
						this.busManagerItem.canType = message.data.canType;
						this.busManagerItem.rackNo = message.data.rackNo;
						this.busManagerItem.nodeId = message.data.nodeId;
						this.busManagerItem.parent.parent.save();
						canBusConfigFileSave();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidChangeViewState(
			() => {
				if (this.panel?.visible) this.render();
				void this.panel.webview.postMessage({
					command: "CanBusMessage",
					data: {
						canPort: "" + this.busManagerItem.canPort,
						baudrate: "" + this.busManagerItem.baudrate,
						crccCan: crccCanSupport,
						canType: this.busManagerItem.canType,
						rackNo: this.busManagerItem.rackNo,
						nodeId: this.busManagerItem.nodeId,
					},
				});
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/can-bus-manager-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
