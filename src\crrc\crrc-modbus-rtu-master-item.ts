import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcModbusRtuMasterUi from "./crrc-modbus-rtu-master-ui";
import {
	type CrrcPlcModbusRtuMasterParams,
	CrrcModbusRtuMasterParams,
} from "@taotech/plc-ide-ui";

export default class CrrcModbusRtuMasterItem extends VscodeTreeItem {
	declare parent: PlcItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-modbus-rtu-master";
	command = {
		title: "Property",
		command: "taotech.crrc.modbus.rtu.master.edit",
		arguments: [this as unknown],
	};

	crrcModbusRtuMasterUi?: CrrcModbusRtuMasterUi;

	constructor(
		public cfg: CrrcModbusRtuMasterParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "Modbus RTU Master", parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcModbusRtuMasterUi) this.crrcModbusRtuMasterUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcModbusRtuMasterUi) {
			this.toJSON();
			this.crrcModbusRtuMasterUi = new CrrcModbusRtuMasterUi(
				this.parent.ip,
				this.parent.token!,
				[this.cfg],
				(params) => this.edit(params),
				() => (this.crrcModbusRtuMasterUi = undefined),
			);
		} else this.crrcModbusRtuMasterUi.reveal();
	}

	edit(params: CrrcPlcModbusRtuMasterParams) {
		this.cfg = params[0];
		ide.solution.refresh(this);
		this.parent.parent.save();
	}

	remove() {
		this.parent.parent.removeChild(this.parent, this);
	}
}
