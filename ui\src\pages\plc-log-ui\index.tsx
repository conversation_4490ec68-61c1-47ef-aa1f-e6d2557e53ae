import { useState, useEffect, useRef } from "react";

import {
	Typography,
	Table,
	TableColumnProps,
	Input,
	DatePicker,
	Drawer,
	Form,
	InputNumber,
	Select,
	Switch,
	Notification,
	Tooltip,
} from "@arco-design/web-react";
import { IconBytedanceColor, IconSettings } from "@arco-design/web-react/icon";
import BarSvg from "./icon/bar.svg?react";

const { Title } = Typography;

import type EChartsReact from "echarts-for-react";
import LogChart from "./LogChart";

import {
	MaterialReactTable,
	type MRT_ColumnFiltersState,
	type MRT_PaginationState,
	MRT_ToggleFullScreenButton,
} from "material-react-table";
import { MRT_Localization_ZH_HANS } from "material-react-table/locales/zh-Hans";

import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { zhCN as zhCNDatePicker } from "@mui/x-date-pickers/locales";

import { createTheme, ThemeProvider, useTheme } from "@mui/material";
import { zhCN } from "@mui/material/locale";

import { vscodeWeb } from "@taotech/libweb";
import { apiUrl, jsonRpcSubscribe, jsonRpcCall, JsonRpcRequest, WebSock } from "@taotech/libdual";

import {
	type Log,
	type Logs,
	type LogStats,
	GetLogStorePathReq,
	GetLogStorePathOk,
	GetLogStorePathErr,
	StoreLogsNotify,
	type GetLogsParams,
	GetLogsNotify,
	type SetLogsParams,
	SetLogsNotify,
} from "@/msg.ts";

interface LogParam {
	fileSize: number;
	fileCount: number;
	level: string;
	file: boolean;
	line: boolean;
	function: boolean;
}

const levels = ["Trace", "Debug", "Info", "Warning", "Error", "Exception", "Fatal"];

const root = document.getElementById("root")!;
const ip = root.dataset.ip!;
const token = root.dataset.token!;
const task = root.dataset.task;

const datetimeForamtter = new Intl.DateTimeFormat("zh-CN", {
	dateStyle: "short",
	timeStyle: "medium",
});

function getThemeName(dark: boolean) {
	return dark ? "dark" : "";
}

function createTableTheme(dark: boolean) {
	return createTheme(
		{
			colorSchemes: {
				dark: dark,
			},
		},
		zhCNDatePicker,
		zhCN,
	);
}

export default function App() {
	const [logs, setLogs] = useState<Logs>([]);

	const [tableTheme, setTableTheme] = useState(createTableTheme(vscodeWeb.colorSchemeIsDark()));
	const [isLoading, setIsLoading] = useState(false);
	const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([]);
	const [rowCount, setRowCount] = useState(0);
	const [pagination, setPagination] = useState<MRT_PaginationState>({
		pageIndex: 0,
		pageSize: 10,
	});
	useEffect(() => {
		setIsLoading(true);
		vscodeWeb.postMessage(new GetLogsNotify({ columnFilters, pagination }));
	}, [columnFilters, pagination]);

	useEffect(() => {
		vscodeWeb.colorSchemeListen((dark) => {
			setTableTheme(createTableTheme(dark));
			setChartTheme(getThemeName(dark));
		});

		window.addEventListener("message", (e: MessageEvent<SetLogsNotify>) => {
			if (!e || !e.data || e.data.method !== SetLogsNotify.METHOD) return;
			setRowCount(e.data.params.totalRowCount);
			setLogs(e.data.params.logs);
			setStats(e.data.params.stats);
			setIsLoading(false);
		});
	}, []);

	const statChart = useRef<EChartsReact>(null);
	const [chartTheme, setChartTheme] = useState(getThemeName(vscodeWeb.colorSchemeIsDark()));
	const [stats, setStats] = useState<LogStats>({});
	const [statsType, setStatsType] = useState<"line" | "bar" | undefined>();

	useEffect(() => {
		if (!statsType) return;

		const statsLeves = Object.keys(stats);

		const xAxis = {
			type: "category",
			data: statsLeves,
		};

		const components = new Set<string>();
		for (const level in stats) {
			for (const component in stats[level]) {
				components.add(component);
			}
		}

		let series = Array.from(components).map((component) => {
			return {
				name: component,
				type: statsType,
				data: statsLeves.map((level) => {
					return stats[level][component] || 0;
				}),
				barMaxWidth: 40,
				label: {
					show: true,
				},
			};
		});

		statChart.current?.getEchartsInstance().setOption(
			{
				xAxis,
				series,
			},
			{ replaceMerge: ["xAxis", "series"] },
		);
	}, [stats, statsType]);

	const [spinConfigButton, setSpinConfigButton] = useState(false);
	const [paramGot, setParamGot] = useState<LogParam>();
	const [configForm] = Form.useForm();

	return (
		<div className="container mx-4">
			<div>
				<Title type="primary" className="text-center">
					日志
				</Title>
			</div>
			<div className="mb-2 flex justify-end gap-x-4">
				<Tooltip content="柱状统计图" triggerProps={{ mouseEnterDelay: 1000 }}>
					<BarSvg
						className="arco-icon"
						style={{ fontSize: 24 }}
						onClick={() => {
							if (!statsType) setStatsType("bar");
							else if (statsType === "bar") setStatsType("line");
							else setStatsType(undefined);
						}}
					/>
				</Tooltip>
				<Tooltip content="配置" triggerProps={{ mouseEnterDelay: 1000 }}>
					<IconSettings
						style={{ fontSize: 24 }}
						spin={spinConfigButton}
						onClick={() => {
							setSpinConfigButton(true);
							jsonRpcCall<LogParam, { token: string }>(
								apiUrl(ip, "log"),
								new JsonRpcRequest("getPattern", { token }),
							)
								.then((result) => {
									setParamGot(result);
								})
								.catch((reason) => {
									console.error(reason);
									Notification.error({
										content: "获取参数失败，请确认设备在线后重试",
									});
								})
								.finally(() => setSpinConfigButton(false));
						}}
					/>
				</Tooltip>
			</div>
			<div>
				<ThemeProvider theme={tableTheme}>
					<LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
						<MaterialReactTable
							data={logs}
							positionPagination="top"
							muiFilterDateTimePickerProps={{
								views: ["year", "month", "day", "hours", "minutes", "seconds"],
								timeSteps: {
									hours: 1,
									minutes: 1,
									seconds: 1,
								},
							}}
							renderToolbarInternalActions={({}) => <></>}
							localization={MRT_Localization_ZH_HANS}
							state={{
								isLoading,
								columnFilters,
								pagination,
							}}
							columnFilterDisplayMode="popover"
							manualFiltering={true}
							onColumnFiltersChange={setColumnFilters}
							rowCount={rowCount}
							enableSorting={false}
							manualPagination={true}
							onPaginationChange={setPagination}
							layoutMode="grid"
							columns={[
								{
									header: "组件",
									accessorKey: "component",
									size: 50,
								},
								{
									header: "级别",
									accessorKey: "level",
									filterVariant: "multi-select",
									filterSelectOptions: levels,
									size: 50,
								},
								{
									header: "时间",
									accessorKey: "time",
									Cell: ({ row }) => {
										return datetimeForamtter.format(row.original.time);
									},
									filterVariant: "datetime-range",
									size: 50,
								},
								{
									header: "内容",
									accessorKey: "text",
									grow: true,
								},
							]}
						/>
					</LocalizationProvider>
				</ThemeProvider>
			</div>
			{statsType && (
				<LogChart
					ref={statChart}
					theme={chartTheme}
					className="mt-8"
					style={{ height: "160px", width: "100%" }}
					option={{
						animation: false,
						xAxis: {
							type: "category",
						},
						yAxis: {
							type: "value",
							axisTick: {
								show: false,
							},
							axisLabel: {
								show: false,
							},
							axisLine: {
								show: false,
							},
							splitLine: {
								show: false,
							},
						},
						legend: {},
						series: [],
					}}
				/>
			)}
			{paramGot && (
				<Drawer
					placement={"right"}
					visible
					width={400}
					title={<span>Log settings</span>}
					onOk={() => {
						configForm
							.validate()
							.then(() => {
								jsonRpcCall<boolean, LogParam>(
									apiUrl(ip, "log"),
									new JsonRpcRequest("setPattern", {
										...(configForm.getFields() as LogParam),
										token,
									}),
								)
									.then((result) => {
										setParamGot(undefined);
									})
									.catch((reason) => {
										console.error(reason);
										Notification.error({
											content: "设置参数失败，请确认设备在线后重试",
										});
									});
							})
							.catch(() => {});
					}}
					onCancel={() => setParamGot(undefined)}
				>
					<Form
						form={configForm}
						labelCol={{
							span: 16,
						}}
						wrapperCol={{
							span: 8,
						}}
						requiredSymbol={false}
					>
						<Form.Item
							label="每个文件大小（KB)"
							field="fileSize"
							initialValue={paramGot.fileSize}
							rules={[{ required: true }]}
						>
							<InputNumber></InputNumber>
						</Form.Item>
						<Form.Item
							label="文件最大个数"
							field="fileCount"
							initialValue={paramGot.fileCount}
							rules={[{ required: true }]}
						>
							<InputNumber></InputNumber>
						</Form.Item>
						<Form.Item
							label="级别"
							field="level"
							initialValue={paramGot.level}
							rules={[{ required: true }]}
						>
							<Select options={levels}></Select>
						</Form.Item>
						<Form.Item
							label="记录文件名"
							field="file"
							triggerPropName="checked"
							initialValue={paramGot.file}
							rules={[{ required: true }]}
						>
							<Switch />
						</Form.Item>
						<Form.Item
							label="记录文件行号"
							field="line"
							triggerPropName="checked"
							initialValue={paramGot.line}
							rules={[{ required: true }]}
						>
							<Switch />
						</Form.Item>
						<Form.Item
							label="记录函数名"
							field="function"
							triggerPropName="checked"
							initialValue={paramGot.function}
							rules={[{ required: true }]}
						>
							<Switch />
						</Form.Item>
					</Form>
				</Drawer>
			)}
		</div>
	);
}
