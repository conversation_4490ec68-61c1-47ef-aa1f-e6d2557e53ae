import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

export default class CrrcRbmUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(title: string, ...disposes: Disposes) {
		super(...disposes);

		this.panel = vscode.window.createWebviewPanel(
			"CrrcRcmProperty",
			`${title} 属性`,
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		this.willDispose(this.panel);

		this.render();

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "assets", "index.css"]);

		const scriptUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "assets", "index.js"]);

		const productUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "RBM-2SFP.jpg"]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!TICTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root" data-product-url="${productUri}"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/crrc-rbm-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
