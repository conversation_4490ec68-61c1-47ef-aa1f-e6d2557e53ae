{"type": "module", "name": "@taotech/plcide-ui", "version": "1.0.0", "private": true, "main": "./dist/cjs/msg.js", "module": "./dist/esm/msg.js", "exports": {".": {"require": "./dist/cjs/msg.js", "import": "./dist/esm/msg.js"}}, "scripts": {"start": "vite", "build": "tsc -b && vite build", "postbuild": "..\\make-sub-package-json.bat"}, "dependencies": {"@arco-design/web-react": "^2.64.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.1", "echarts-for-react": "^3.0.2", "material-react-table": "^3.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.1.5", "react-usestateref": "^1.0.9"}, "devDependencies": {"vite-plugin-svgr": "^4.3.0", "rollup-plugin-visualizer": "^5.9.0", "@rollup/plugin-commonjs": "^27.0.0"}}