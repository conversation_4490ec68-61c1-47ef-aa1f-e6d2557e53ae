import { useState } from "react";
import { Tree, Form, Space, Button, Layout, Modal } from "@arco-design/web-react";
import { IconDelete, IconPlus, IconStop } from "@arco-design/web-react/icon";
import { vscodeWeb } from "@taotech/libweb";


enum BUSEDITTYPE {
	BUSEDITTYPE_NULL = "null",
	BUSEDITTYPE_BUS = "bus",
	BUSEDITTYPE_OTHER = "other",
}

export default function App() {
	const [siderWidth, setSiderWidth] = useState(220);
	const [deleteDisible, setDeleteDisible] = useState(false);
	const [visible, setVisible] = useState(true); // table
	const [editType, setEditType] = useState(BUSEDITTYPE.BUSEDITTYPE_BUS);

	function busManagerButtonAddClick() {
		vscodeWeb.postMessage({
			command: "BusSelected",
			selectedItem: selectedKeys,
		});
		setVisible(false);
	}

	function busManagerButtonCancelClick() {
		vscodeWeb.postMessage({
			command: "BusCancel",
		});
		setVisible(false);
	}

	function busDeleteConfirmClick() {
		setDeleteDisible(false);
		let path = "";
		treeData.forEach((node) => { // 遍历 treeData 数组中的每个节点
			if (node.children !== undefined) { // 检查当前节点是否有 children 属性
				node.children.forEach((item) => { // 如果有 children 属性，则遍历 children 数组
					if (selectedKeys.includes(item.key)) {
						const index = node.children!.indexOf(item); // 找到要删除项的索引
						if (index > -1) {
							path = node.children![index].path; // 找到要删除项的路径	
							node.children!.splice(index, 1); // 删除该项
							vscodeWeb.postMessage({
								command: "BusDelete",
								selectedItem : [path],
							});
						}
					}
				});
			}
		});
	}

	function busManagerButtonDeleteClick() {
		setDeleteDisible(true);
	}

	const optionVernoMap = [
		{
			key: "CANbus",
			name: "CANbus",
			supplier: "TAOTECH Technology Co., Ltd.",
			category: "CAN总线",
			version: "1.0.0.0",
		},
	];

	const [formData, setFormData] = useState({
		key: "CANbus",
		name: "CANbus",
		supplier: "TAOTECH Technology Co., Ltd.",
		category: "CAN总线",
		version: "1.0.0.0",
	});

	const [OptionVernoMap, setOptionVernoMap] = useState([...optionVernoMap]);

	const [selectedKeys, setSelectedKeys] = useState([""]);
	const [expandedKeys, setExpandedKeys] = useState([""]);
	const [expandedInitKeys, setExpandedInitKeys] = useState([""]);
	const onOptionSelect = (keys: string[]) => {
		setExpandedKeys(["0-0", ...expandedInitKeys]);
		setSelectedKeys([...keys]);
	};

	interface PopTreeNode {
		title: string;
		key: string;
		path: string;
		selectable?: boolean;
		children?: PopTreeNode[];
	}

	interface CanBusMessageEvent {
		data: {
			command: string;
			treeData: PopTreeNode[];
			type: BUSEDITTYPE;
			optionVernoMap: { [key: string]: string };
			selectedKeys: string[];
			expandedKeys: string[];
		};
	}

	window.addEventListener("message", (event: CanBusMessageEvent) => {
		const message = event.data;
		if (message.command === "updateTreeData") {
			setTreeData(message.treeData);
			setEditType(message.type);
			setOptionVernoMap([...optionVernoMap]);
			setSelectedKeys([...message.selectedKeys]);
			setExpandedKeys([...message.expandedKeys]);
			setExpandedInitKeys([...message.expandedKeys]);
		}
	});

	const [treeData, setTreeData] = useState([] as PopTreeNode[]);

	const sider = (
		<Layout>
			<Layout.Sider width={siderWidth}>
				<Tree
					expandedKeys={expandedKeys}
					selectedKeys={selectedKeys}
					treeData={treeData}
					onSelect={onOptionSelect}
				/>
			</Layout.Sider>
			<Layout.Content style={{ textAlign: "left", padding: "30px" }}>
				<div style={{ width: "100%", height: "100%" }}>
					<Form style={{ width: 600 }} autoComplete="off">
						<Form.Item>名称: {formData.name}</Form.Item>
						<Form.Item>供应商: {formData.supplier}</Form.Item>
						<Form.Item>类别: {formData.category}</Form.Item>
						<Form.Item>版本: {formData.version}</Form.Item>
					</Form>
				</div>
			</Layout.Content>
		</Layout>
	);
	return (
		<div>
			{sider}
			<Space size="large"></Space>
			<Space size="large">
				<Button
					type="primary"
					id="addBtn"
					onClick={busManagerButtonAddClick}
					icon={<IconPlus />}
				>
					添加
				</Button>
				{editType === BUSEDITTYPE.BUSEDITTYPE_OTHER && (
					<Button
					type="primary"
					id="DelBtn"
					onClick={busManagerButtonDeleteClick}
					icon={<IconDelete />}
				>
					删除
				</Button>
				)}
				<Button
					type="primary"
					id="cancelBtn"
					onClick={busManagerButtonCancelClick}
					icon={<IconStop />}
				>
					取消
				</Button>
			</Space>
			<div>
				<Modal
					title='删除'
					visible={deleteDisible}
					onOk={() => busDeleteConfirmClick()}
					onCancel={() => setDeleteDisible(false)}
					autoFocus={false}
					focusLock={true}
				>
					<p>
					是否删除选中的项.
					</p>
				</Modal>
			</div>
		</div>
	);
}
