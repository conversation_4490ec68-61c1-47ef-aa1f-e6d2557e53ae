import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import CrrcCANUi from "./crrc-can-ui";
import { type CrrcRacksCANsParams, CrrcCANParams } from "@taotech/plc-ide-ui";
import CrrcRackItem from "./crrc-rack-item";

export default class CrrcCANItem extends VscodeTreeItem {
	declare parent: CrrcRackItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-can";
	command = {
		title: "Property",
		command: "taotech.crrc.can.edit",
		arguments: [this as unknown],
	};

	crrcCANUi?: CrrcCANUi;

	constructor(
		public cfg: CrrcCANParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "CAN " + cfg.index, parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcCANUi) this.crrcCANUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcCANUi) {
			this.toJSON();
			this.crrcCANUi = new CrrcCANUi(
				this.parent.parent.ip,
				this.parent.parent.token!,
				this.parent.getChildFreeIndexies(this.cfg.index),
				[{ index: this.parent.cfg.index, children: [this.cfg] }],
				(params) => this.edit(params),
				() => (this.crrcCANUi = undefined),
			);
		} else this.crrcCANUi.reveal();
	}

	edit(params: CrrcRacksCANsParams) {
		const cfg = params[0].children[0];
		if (!this.parent.isChildIndexValidForEdit(cfg.index, this.cfg.index)) return;
		this.cfg = cfg;
		this.label = "CAN " + this.cfg.index;
		ide.solution.refresh(this);
		this.parent.parent.parent.save();
	}

	remove() {
		this.parent.parent.parent.removeChild(this.parent, this);
	}
}
