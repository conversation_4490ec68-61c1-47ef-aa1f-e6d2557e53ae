import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { vitePluginForArco } from "@arco-plugins/vite-react";
import path from "path";
import svgr from "vite-plugin-svgr";
import { visualizer } from "rollup-plugin-visualizer";
import commonjs from '@rollup/plugin-commonjs';

// https://vitejs.dev/config/
export default defineConfig({
	plugins: [react(), vitePluginForArco(), svgr(), visualizer({
		open: true
	}), commonjs()],
	build: {
		cssCodeSplit: false,
		minify: 'terser',
		terserOptions: {
			compress: {
				drop_console: true,
				drop_debugger: true,
				pure_funcs: ['console.log', 'console.info', 'console.debug'],
				passes: 2
			},
			format: {
				comments: false
			}
		},
		sourcemap: false,
		chunkSizeWarningLimit: 600,
		outDir: "../dist/plc-ide-ui",
		emptyOutDir: true,
		assetsInlineLimit: 999999,
		rollupOptions: {
			output: {
				entryFileNames: "assets/[name].js",
				chunkFileNames: "assets/[name].js",
				assetFileNames: "assets/[name][extname]",
				manualChunks: (id) => {
					return "main"
				}
			},
		},
		treeshake: {
			moduleSideEffects: false,
			propertyReadSideEffects: false,
			tryCatchDeoptimization: false
		}
	},
	optimizeDeps: {
		include: ['react', 'react-dom', 'react-router-dom', '@arco-design/web-react', 'echarts-for-react', "@mui/material", "@mui/icons-material", "@mui/x-date-pickers"],
		entries: ['src/main.tsx'],
		disabled: false
	},

	resolve: {
		alias: {
			"@": path.resolve(__dirname, "src"),
		},
	},
	define: {
		__COCKPIT__: false,
	},
});
