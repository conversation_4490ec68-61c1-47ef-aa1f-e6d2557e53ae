import vscode from "vscode";
import * as fs from "fs";
import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import CanSlaveManagerItem, { CanSlaveManagerCfg } from "./can-slave-manager-item";
import { canopenPriority, EditPdodataType, PdodataType } from "./bus-type";
import PlcItem from "../plc/plc-item";
import BusManagerItem, { projectPath } from "./bus-manager-item";
import CanMasterManagerItem from "./can-master-manager-item";
import ProjectItem from "../project/project-item";

//can主设备设置交互消息类型定义
export interface CanSlaveMessage {
	command: string;
	data: CanSlaveManagerCfg | any;
}

export default class CanSlaveManagerUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;
	canSlaveManagerItem: CanSlaveManagerItem;
	declare projectItem: ProjectItem;
	constructor(canSlaveManagerItem: CanSlaveManagerItem, ...disposes: Disposes) {
		super(...disposes);

		this.canSlaveManagerItem = canSlaveManagerItem;
		this.projectItem = canSlaveManagerItem.parent.parent.parent.parent;

		this.panel = vscode.window.createWebviewPanel(
			"CANBUS",
			"CAN从站设置",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);

		this.willDispose(this.panel);

		void this.panel.webview.postMessage({
			command: "slave",
			slave: { ...this.canSlaveManagerItem },
			protectType: this.canSlaveManagerItem.parent.protectedType(),
		});

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(message: CanSlaveMessage) => {
				switch (message.command) {
					case "slave":
						const data = message.data as CanSlaveManagerCfg;
						this.canSlaveManagerItem.nodeId = data.nodeId!;
						this.canSlaveManagerItem.expertSetting = data.expertSetting!;
						this.canSlaveManagerItem.syncProducer = data.syncProducer!;
						this.canSlaveManagerItem.guarding.nodeProtectDisable =
							data.guarding!.nodeProtectDisable;
						this.canSlaveManagerItem.guarding.heartBeatDisable =
							data.guarding!.heartBeatDisable;
						this.canSlaveManagerItem.guarding.guardingEnable =
							data.guarding!.guardingEnable;
						this.canSlaveManagerItem.guarding.heartBeatProducer =
							data.guarding!.heartBeatProducer;
						this.canSlaveManagerItem.guarding.guardTime = data.guarding!.guardTime;
						this.canSlaveManagerItem.guarding.producerTime =
							data.guarding!.producerTime;
						this.canSlaveManagerItem.guarding.lifeFactor = data.guarding!.lifeFactor;
						this.canSlaveManagerItem.emcy.emcyDisable = data.emcy!.emcyDisable;
						this.canSlaveManagerItem.emcy.emcy = data.emcy!.emcy;
						this.canSlaveManagerItem.emcy.cobId = data.emcy!.cobId;
						this.canSlaveManagerItem.timestamp.timestampDisable =
							data.timestamp!.timestampDisable;
						this.canSlaveManagerItem.timestamp.timeProducer =
							data.timestamp!.timeProducer;
						this.canSlaveManagerItem.timestamp.cobId = data.timestamp!.cobId;
						this.canSlaveManagerItem.check.vendorId = data.check!.vendorId;
						this.canSlaveManagerItem.check.productId = data.check!.productId;
						this.canSlaveManagerItem.check.revisionNumber = data.check!.revisionNumber;
						this.canSlaveManagerItem.rxPdo = [...data.rxPdo!];
						this.canSlaveManagerItem.rxPdoBackup = [...data.rxPdoBackup!];
						this.canSlaveManagerItem.txPdo = [...data.txPdo!];
						this.canSlaveManagerItem.txPdoBackup = [...data.txPdoBackup!];
						this.canSlaveManagerItem.sPdo = [...data.sPdo!];
						this.canSlaveManagerItem.optionRxPdo = [...data.optionRxPdo!];
						this.canSlaveManagerItem.optionTxPdo = [...data.optionTxPdo!];
						this.canSlaveManagerItem.optionSdo = [...data.optionSdo!];
						this.projectItem.save();
						canBusConfigFileSave();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidChangeViewState(
			() => {
				if (this.panel?.visible) this.render();
				void this.panel.webview.postMessage({
					command: "slave",
					slave: { ...this.canSlaveManagerItem },
					protectType: this.canSlaveManagerItem.parent.protectedType(),
				});
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/can-slave-manager-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}

	updateProtectType() {
		void this.panel.webview.postMessage({
			command: "slave",
			slave: { ...this.canSlaveManagerItem },
			protectType: this.canSlaveManagerItem.parent.protectedType(),
		});
	}
}

function dataTypeToDataName(dataType: string | undefined): string {
	let type = 0;

	if (dataType === undefined) {
		return "unknow";
	}

	if (dataType.includes("0x")) {
		type = parseInt(dataType.slice(2), 16);
	} else {
		type = parseInt(dataType, 10);
	}

	switch (type) {
		case 1: //BOOLEAN
			return "bool";
		case 2: //INTEGER8
			return "i8";
		case 3: //INTEGER16
			return "i16";
		case 4: //INTEGER32
			return "i32";
		case 5: //UNSIGNED8
			return "u8";
		case 6: //UNSIGNED16
			return "u16";
		case 7: //UNSIGNED32
			return "u32";
		case 8: //REAL32
			return "r32";
		case 9: //VISIBLE_STRING
			return "VISIBLE_STRING"; //8 * (item.length + 1);
		case 10: //OCTET_STRING
			return "OCTET_STRING"; //8 * item.length;
		case 11: //UNICODE_STRING
			return "UNICODE_STRING"; //16 * (item.length + 1);
		default:
			return "unknow";
	}
}

function dataTypeToBitLength(dataType: string | undefined): number {
	let type = 0;

	if (dataType === undefined) {
		return 32;
	}

	if (dataType.includes("0x")) {
		type = parseInt(dataType.slice(2), 16);
	} else {
		type = parseInt(dataType, 10);
	}

	switch (type) {
		case 1: //BOOLEAN
			return 1;
		case 2: //INTEGER8
			return 8;
		case 3: //INTEGER16
			return 16;
		case 4: //INTEGER32
			return 32;
		case 5: //UNSIGNED8
			return 8;
		case 6: //UNSIGNED16
			return 16;
		case 7: //UNSIGNED32
			return 32;
		case 8: //REAL32
			return 32;
		case 9: //VISIBLE_STRING
			return 32; //8 * (item.length + 1);
		case 10: //OCTET_STRING
			return 32; //8 * item.length;
		case 11: //UNICODE_STRING
			return 32; //16 * (item.length + 1);
		default:
			return 32;
	}
}

function mapTypeToDataName(dataType: number): string {
	switch (dataType) {
		case 1: //BOOLEAN
			return "bool";
		case 8: //INTEGER8
			return "u8";
		case 16: //INTEGER16
			return "u16";
		case 32: //INTEGER32
			return "u32";
		default:
			return "unknow";
	}
}

function sdoDecode(pdos: PdodataType[]) {
	const retSdo: {
		index?: string;
		subIndex?: number;
		type?: string;
		value?: string;
		mapType?: string;
		AccessType?: string;
	}[] = [];

	for (let i = 0; i < pdos.length; i++) {
		let subEntry: {
			index?: string;
			subIndex?: number;
			type?: string;
			value?: string;
			mapType?: string;
			AccessType?: string;
		};

		let accessType = pdos[i].editProperty.accessType?.toLocaleLowerCase();

		subEntry = {
			index: "0x" + pdos[i].index,
			subIndex: pdos[i].editProperty.subIndex,
			type: dataTypeToDataName(pdos[i].editProperty.dataType),
			value: "0x" + pdos[i].editProperty.defaultValue!.slice(3),
			AccessType: accessType,
		};

		retSdo[i] = { ...subEntry };
	}

	return retSdo;
}

interface pdoOutType {
	[slaveKey: string]: {
		index?: string;
		subIndex?: number;
		type?: string;
		value?: string;
		mapType?: string;
		property?: {
			cobId: number;
			suppressTime: number;
			syncTime: number;
			eventTime: number;
			transfertype: string;
		};
	}[];
}

function pdoDecode(pdos: PdodataType[], type: string, nodeId: number) {
	let iPdo = 0;
	const retPdo: pdoOutType = {};

	for (let i = 0; i < pdos.length; i++) {
		const subEntry: {
			index?: string;
			subIndex?: number;
			type?: string;
			value?: string;
			mapType?: string;
			AccessType?: string;
			property?: {
				cobId: number;
				suppressTime: number;
				syncTime: number;
				eventTime: number;
				transfertype: string;
			};
		}[] = [];

		if (pdos[i].defaultValue & 0x80000000) {
			continue;
		}
		const pdosName = type + iPdo;
		iPdo++;

		let accessType = pdos[i].editProperty.accessType?.toLocaleLowerCase();
		if (accessType && accessType.toLowerCase().includes("rw")) {
			accessType = "rw";
		}

		subEntry[0] = {
			index: "0x" + pdos[i].index!.toUpperCase(),
			subIndex: pdos[i].editProperty.subIndex,
			property: {
				cobId: pdos[i].property!.cobId + nodeId,
				suppressTime: pdos[i].property!.suppressTime,
				syncTime: pdos[i].property!.syncTime,
				eventTime: pdos[i].property!.eventTime,
				transfertype: pdos[i].property!.transfertype,
			},
			type: dataTypeToDataName(pdos[i].editProperty.dataType),
			value: "0x" + pdos[i].defaultValue.toString(16),
			AccessType: accessType,
		};
		if (
			pdos[i].editProperty.pDOMappingBitLength !== undefined &&
			pdos[i].editProperty.pDOMappingBitLength !==
				dataTypeToBitLength(pdos[i].editProperty.dataType)
		) {
			subEntry[0].mapType = mapTypeToDataName(pdos[i].editProperty.pDOMappingBitLength);
		}

		for (let j = 0; j < pdos[i].subLength; j++) {
			accessType = pdos[i].children[j].editProperty.accessType?.toLocaleLowerCase();
			if (accessType && accessType.toLowerCase().includes("rw")) {
				accessType = "rw";
			}
			subEntry[j + 1] = {
				index: "0x" + (parseInt(pdos[i].index!, 16) + 0x200).toString(16).toUpperCase(),
				subIndex: j + 1,
				type: "u32",
				mapType: dataTypeToDataName(pdos[i].children[j].editProperty.dataType),
				value: "0x" + pdos[i].children[j].defaultValue.toString(16),
				AccessType: accessType,
			};

			if (
				pdos[i].children[j].editProperty.pDOMappingBitLength !== undefined &&
				pdos[i].children[j].editProperty.pDOMappingBitLength !==
					dataTypeToBitLength(pdos[i].children[j].editProperty.dataType)
			) {
				subEntry[j + 1].mapType = mapTypeToDataName(
					pdos[i].children[j].editProperty.pDOMappingBitLength,
				);
			}
		}
		retPdo[pdosName] = [...subEntry];
	}

	return [retPdo, iPdo];
}

function materDecode(canMaster: canMasterConfig[]) {
	let salveNum = 0;
	const masterDataArray = [];
	for (let i = 0; i < canMaster.length; i++) {
		const slaveData: {
			[slaveKey: string]: {
				name: string;
				nodeId: number;
				rpdoNum: number;
				tpdoNum: number;
				spdoNum: number;
				expertSetting: boolean;
				syncProducer: boolean;
				guarding: {
					guardingEnable: boolean;
					heartBeatProducer: boolean;
					guardTime: string;
					producerTime: string;
					lifeFactor: string;
				};
				emcy: { emcy: boolean; cobId: string };
				timestamp: {
					timeProducer: boolean;
					cobId: string;
					timeConsumer: boolean;
				};
				check: { vendorId: boolean; productId: boolean; revisionNumber: boolean };
				rPdos: pdoOutType;
				tPdos: pdoOutType;
				sdos?: {
					index?: string;
					subIndex?: number;
					type?: string;
					value?: string;
					mapType?: string;
				}[];
			};
		} = {};

		if (canMaster[i].type === CanMasterManagerItem.name && canMaster[i].canSlaveNum > 0) {
			const canSlave = canMaster[i].children!;
			for (let l = 0; l < canSlave.length; l++) {
				if (canSlave[l].type === CanSlaveManagerItem.name) {
					const slaveName = `slave${l}`;
					const [rxPdo, rxNum] = pdoDecode(
						canSlave[l].rxPdo,
						"rPdo",
						parseInt(canSlave[l].nodeId),
					) as [pdoOutType, number];
					const [txPdo, txNum] = pdoDecode(
						canSlave[l].txPdo,
						"tPdo",
						parseInt(canSlave[l].nodeId),
					) as [pdoOutType, number];

					salveNum++;
					slaveData[slaveName] = {
						name: canSlave[l].name,
						nodeId: parseInt(canSlave[l].nodeId),
						rpdoNum: rxNum,
						tpdoNum: txNum,
						spdoNum: canSlave[l].sPdo.length,
						expertSetting: canSlave[l].expertSetting,
						syncProducer: canSlave[l].syncProducer,
						guarding: {
							guardingEnable: canSlave[l]["guarding"].guardingEnable,
							heartBeatProducer: canSlave[l]["guarding"].heartBeatProducer,
							guardTime: canSlave[l]["guarding"].guardTime,
							producerTime: canSlave[l]["guarding"].producerTime,
							lifeFactor: canSlave[l]["guarding"].lifeFactor,
						},
						emcy: {
							emcy: canSlave[l]["emcy"].emcy,
							cobId: canSlave[l]["emcy"].cobId,
						},
						timestamp: {
							timeProducer: canSlave[l]["timestamp"].timeProducer,
							cobId: canSlave[l]["timestamp"].cobId,
							timeConsumer: canSlave[l]["timestamp"].timeConsumer,
						},
						check: {
							vendorId: canSlave[l]["check"].vendorId,
							productId: canSlave[l]["check"].productId,
							revisionNumber: canSlave[l]["check"].revisionNumber,
						},
						rPdos: rxPdo,
						tPdos: txPdo,
					};

					if (canSlave[l].sPdo.length > 0) {
						const sPdo = sdoDecode(canSlave[l].sPdo);
						slaveData[slaveName].sdos = sPdo;
					}
				}
			}
		}

		const masterData = {
			nodeId: parseInt(canMaster[i].nodeId),
			slaveNum: salveNum,
			autoStartManger: canMaster[i].autoStartManger,
			autoStartSlave: canMaster[i].autoStartSlave,
			slaveEnablePolling: canMaster[i].slaveEnablePolling,
			NMTErrorBehavior: canMaster[i].nMTErrorBehavior,
			guarding: {
				nodeId: parseInt(canMaster[i]["guarding"].nodeId),
				producerTime: parseInt(canMaster[i]["guarding"].producerTime),
				enableHeartbeat: canMaster[i]["guarding"].enableHeartbeat,
			},
			sync: {
				enableSyncproduce: canMaster[i]["sync"].enableSyncproduce,
				cobId: parseInt(canMaster[i]["sync"].cobId, 16),
				cyclePeriod: parseInt(canMaster[i]["sync"].cyclePeriod),
				windowLength: parseInt(canMaster[i]["sync"].windowLength),
				enableSyncConsume: canMaster[i]["sync"].enableSyncConsume,
			},
			timestamp: {
				enableTime: canMaster[i]["timestamp"].enableTime,
				cobId: parseInt(canMaster[i]["timestamp"].cobId, 16),
				producerTime: parseInt(canMaster[i]["timestamp"].producerTime),
			},
			slaves: slaveData,
		};
		masterDataArray.push(masterData);
	}

	return masterDataArray;
}

interface canSlaveConfig {
	label: string;
	type: string;
	index: number;
	name: string;
	path: string;
	nodeId: string;
	expertSetting: boolean;
	syncProducer: boolean;
	guarding: {
		nodeProtectDisable: boolean;
		heartBeatDisable: boolean;
		guardingEnable: boolean;
		heartBeatProducer: boolean;
		guardTime: string;
		producerTime: string;
		lifeFactor: string;
	};
	emcy: { emcyDisable: boolean; emcy: boolean; cobId: string };
	timestamp: {
		timestampDisable: boolean;
		timeProducer: boolean;
		cobId: string;
		timeConsumer: boolean;
	};
	check: { vendorId: boolean; productId: boolean; revisionNumber: boolean };
	rxPdo: PdodataType[];
	rxPdoBackup: PdodataType[];
	txPdo: PdodataType[];
	sPdo: PdodataType[];
	txPdoBackup: PdodataType[];
	optionRxPdo: EditPdodataType[];
	optionTxPdo: EditPdodataType[];
	optionSdo: EditPdodataType[];
}
interface canMasterConfig {
	label: string;
	type: string;
	index: number;
	canSlaveNum: number;
	guardingCollapse: string[];
	syncCollapse: string[];
	timestampCollapse: string[];
	nodeId: string;
	autoStartManger: boolean;
	autoStartSlave: boolean;
	slaveEnablePolling: boolean;
	nMTErrorBehavior: string;
	guarding: {
		nodeId: string;
		producerTime: string;
		enableHeartbeat: boolean;
	};
	sync: {
		enableSyncproduce: boolean;
		cobId: string;
		cyclePeriod: string;
		windowLength: string;
		enableSyncConsume: boolean;
	};
	timestamp: {
		enableTime: boolean;
		cobId: string;
		producerTime: string;
	};
	children?: canSlaveConfig[];
}

interface canBusConfig {
	label: string;
	type: string;
	index: number;
	canMasterNum: number;
	canPort: number;
	baudrate: number;
	canType: number;
	rackNo: number;
	nodeId: number;
	children?: canMasterConfig[];
}
interface plcItemConfig {
	label: string;
	type: string;
	busNum: number;
	children?: canBusConfig[];
}
interface JsonObject {
	children?: plcItemConfig[];
	// 其他可能的字段
}

function dataTypeToDefine(dataType: string | undefined) {
	let type = 0;

	if (dataType === undefined) {
		return "unknow";
	}

	if (dataType.includes("0x")) {
		type = parseInt(dataType.slice(2), 16);
	} else {
		type = parseInt(dataType, 10);
	}

	switch (type) {
		case 1: //BOOLEAN
			return ["UNS8", "DATA_TYPE_U8", "VAR_ACC_BOOL8", "1"];
		case 2: //INTEGER8
			return ["INTEGER8", "DATA_TYPE_I8", "VAR_ACC_SINT8", "1"];
		case 3: //INTEGER16
			return ["INTEGER16", "DATA_TYPE_I16", "VAR_ACC_SINT16", "2"];
		case 4: //INTEGER32
			return ["INTEGER32", "DATA_TYPE_I32", "VAR_ACC_SINT32", "4"];
		case 5: //UNSIGNED8
			return ["UNS8", "DATA_TYPE_U8", "VAR_ACC_UINT8", "1"];
		case 6: //UNSIGNED16
			return ["UNS16", "DATA_TYPE_U16", "VAR_ACC_UINT16", "2"];
		case 7: //UNSIGNED32
			return ["UNS32", "DATA_TYPE_U32", "VAR_ACC_UINT32", "4"];
		case 8: //REAL32
			return [];
		case 9: //VISIBLE_STRING
			return []; //
		case 9: //VISIBLE_STRING
			return []; //"VISIBLE_STRING"; //8 * (item.length + 1);
		case 10: //OCTET_STRING
			return []; //"OCTET_STRING"; //8 * item.length;
		case 11: //UNICODE_STRING
			return []; //"UNICODE_STRING"; //16 * (item.length + 1);
		default:
			return []; //"unknow";
	}
}

function pdoConfig(pdos: PdodataType[], type: string, bus: number, master: number, nodeId: number) {
	let iPdo = 0;
	const retPdo: pdoOutType = {};
	const pdoMapDefine = [] as string[];
	const pdoMonitorDefine = [] as string[];
	const pdoNameDefine = [] as string[];

	for (let i = 0; i < pdos.length; i++) {
		let mirco = "";
		if (pdos[i].defaultValue & 0x80000000) {
			continue;
		}
		const pdosName = `GB${bus}M${master}S${nodeId}${type}${i}`;
		iPdo++;

		const pdo = parseInt(pdos[i].index!, 16).toString(16).toUpperCase();

		for (let j = 0; j < pdos[i].subLength; j++) {
			const defaultValue = pdos[i].children[j].defaultValue.toString(16);
			const index = parseInt(defaultValue.slice(0, 4), 16).toString(16).toUpperCase();
			const subIndex = `${parseInt(defaultValue.slice(4, 6), 16).toString(16).toUpperCase().padStart(2, "0")}`;
			const name =
				pdos[i].children[j].editProperty.parameterName === undefined
					? `${pdo}_${index}_${subIndex}`
					: pdos[i].children[j].editProperty
							.parameterName!.replace(/\s+/g, "_")
							.replace(/[^a-zA-Z0-9_]/g, "");
			const variablesName = `${pdosName}P${j}_${name}`;
			const [dataType, defineType, accessType, size] = dataTypeToDefine(
				pdos[i].children[j].editProperty.dataType,
			);
			const sMaster = `0x${bus.toString(16).toUpperCase().padStart(2, "0")}`;
			const sNodeId = `0x${nodeId.toString(16).toUpperCase().padStart(2, "0")}`;
			pdoMapDefine.push(
				`\t\tPDO_MAP(${variablesName}, ${dataType}, ${sMaster}, ${sNodeId}, 0x${pdo}, ${defineType}, 0x${index}, 0x${subIndex}); \\`,
			);
			pdoMonitorDefine.push(`\t\tPDO_MONITOR(${variablesName}, ${accessType}, ${size}); \\`);
			pdoNameDefine.push(`static\t${dataType}\t*${variablesName} = NULL;`);
		}
	}
	return [pdoNameDefine, pdoMapDefine, pdoMonitorDefine];
}

function canBusConfigFileGenerate() {
	const canBusOutConfigHFile = ide.pathJoin(projectPath, "canOpenAutoGen.h");
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始，所以需要加1，并且用 padStart 确保是两位数
	const day = String(now.getDate()).padStart(2, "0"); // 用 padStart 确保是两位数
	const hours = String(now.getHours()).padStart(2, "0"); // 用 padStart 确保是两位数
	const minutes = String(now.getMinutes()).padStart(2, "0"); // 用 padStart 确保是两位数
	const seconds = String(now.getSeconds()).padStart(2, "0"); // 用 padStart 确保是两位数
	const canHfileHead = `
/**************************************************************************************************--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------45 mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[]                       ]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]]
** ©20xx-20xx, TaoTech Technology Co. Ltd
**
** All Rights Reserved
**
**-------------- 文 件 信 息------------------------------------------------------------------------------
**
** 文 件 名: canOpenAutoGen.h
**
** 创 建 人: IDE自动生成
**
** 文件创建日期: ${year} 年 ${month} 月 ${day} 日 ${hours} 时 ${minutes} 分 ${seconds} 秒
**
** 描 述: CANOPEN 模块头文件
**
** 修 改 记 录:
*********************************************************************************************************/
#ifndef __CANOPEN_AUTOGEN_H__
#define __CANOPEN_AUTOGEN_H__
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <unistd.h>
#include <stdint.h>
#include <sys/SysVarAccess.h>

#ifdef __cplusplus
extern "C"
{
#endif
/*********************************************************************************************************
 宏定义
*********************************************************************************************************/

/* 数据类型定义 */
typedef enum data_type
{
	DATA_TYPE_I8 = 0,
	DATA_TYPE_U8,
	DATA_TYPE_I16,
	DATA_TYPE_U16,
	DATA_TYPE_I24,
	DATA_TYPE_U24,
	DATA_TYPE_I32,
	DATA_TYPE_U32,
	DATA_TYPE_I40,
	DATA_TYPE_U40,
	DATA_TYPE_I48,
	DATA_TYPE_U48,
	DATA_TYPE_I56,
	DATA_TYPE_U56,
	DATA_TYPE_I64,
	DATA_TYPE_U64,
	DATA_TYPE_R32,
	DATA_TYPE_R64,
	DATA_TYPE_MAX,
} DATA_TYPE;

/* NMT状态枚举 */
typedef enum nmt_state
{
	NMT_NONE = 0,
	NMT_START_NODE = 0x01,
	NMT_STOP_NODE = 0x02,
	NMT_ENTER_PREOPERATIONAL = 0x80,
	NMT_RESET_NODE = 0x81,
	NMT_RESET_COMUNICATION = 0x82,
} NMT_STATE;
/* Integers */
#define INTEGER8 int8_t
#define INTEGER16 int16_t
#define INTEGER24 int32_t
#define INTEGER32 int32_t
#define INTEGER40 int64_t
#define INTEGER48 int64_t
#define INTEGER56 int64_t
#define INTEGER64 int64_t

/* Unsigned integers */
#define UNS8 uint8_t
#define UNS16 uint16_t
#define UNS32 uint32_t
#define UNS24 uint32_t
#define UNS40 uint64_t
#define UNS48 uint64_t
#define UNS56 uint64_t
#define UNS64 uint64_t

#define MASTER_NUM 8

/*********************************************************************************************************
 外部符号
*********************************************************************************************************/
/*********************************************************************************************************
** 函数名称: dataMapFind
** 功能描述: 查找数据映射表
** 输  入  : uiMasterIndex-主站索引, ucId-ID编号, usPdoIndex-PDO索引,
			type-数据类型， usIndex-索引， ucSubIndex-子索引
** 输  出  : 字典类目
** 返回值  : 映射变量地址
*********************************************************************************************************/
#if MASTER_NUM > 1
void *dataMapFind(unsigned int uiMasterIndex, uint8_t ucId, uint16_t usPdoIndex,
					  DATA_TYPE type, uint16_t usIndex, uint8_t ucSubIndex);
#else
void *dataMapFind(uint8_t ucId, uint16_t usPdoIndex,
				  DATA_TYPE type, uint16_t usIndex, uint8_t ucSubIndex);
#endif

/*********************************************************************************************************
** 函数名称: sdoValueFind
** 功能描述: 通过从站ID获取SDO配置时读取的数值
** 输 入  : uiMasterIndex-主站索引, ucNodeId-从站ID, usIndex-SDO主索引, ucSubIndex-SDO子索引, 
			ulSize-SDO读取大小(字节), pvVal-读取值
** 输 出  : NONE
** 返回值  : =0  -正确，<0 -错误
*********************************************************************************************************/
#if MASTER_NUM > 1
int sdoValueFind(unsigned int uiMasterIndex, UNS8 ucNodeId, UNS16 usIndex,
					 UNS8 ucSubIndex, UNS32 ulSize, void *pvVal);
#else
int sdoValueFind(UNS8 ucNodeId, UNS16 usIndex,
				 UNS8 ucSubIndex, UNS32 ulSize, void *pvVal);
#endif

/*********************************************************************************************************
** 函数名称: canBusStart
** 功能描述: 启动can bus
** 输  入  : NONE
** 输    出: NONE
** 全局变量:
** 调用模块:
*********************************************************************************************************/
void canBusStart(void);

/*********************************************************************************************************
** 函数名称: canBusStop
** 功能描述: 关闭can bus
** 输  入  : NONE
** 输    出: NONE
** 全局变量:
** 调用模块:
*********************************************************************************************************/
void canBusStop(void);

/*********************************************************************************************************
** 函数名称: nmtState
** 功能描述: 获取从站NMT状态
** 输 入  : uiMasterIndex-主站索引, ucNodeId-从站ID
** 输 出  : NONE
** 返回值  : NMT状态
*********************************************************************************************************/
#if MASTER_NUM > 1
NMT_STATE nmtState(unsigned int uiMasterIndex, UNS8 ucNodeId);
#else
NMT_STATE nmtState(UNS8 ucNodeId);
#endif

/*********************************************************************************************************
  PDO 变量映射添加宏
*********************************************************************************************************/
#define PDO_MAP(pdoPtr, dataTyep, master, codeId, pdo, pdoType, index, subIndex)                              \\
	do                                                                                                        \\
	{                                                                                                         \\
		pdoPtr = (dataTyep *)dataMapFind(master, codeId, pdo, pdoType, index, subIndex);                      \\
		if (NULL == pdoPtr)                                                                                   \\
		{                                                                                                     \\
			printf("map %s map type %s master %d slave %d pdo 0x%x type %s index 0x%x subIndex 0x%x error\\n", \\
				   #pdoPtr, #dataTyep, master, codeId, pdo, #pdoType, index, subIndex);                       \\
		}                                                                                                     \\
	} while (0)

/*********************************************************************************************************
  PDO 监控变量添加宏
*********************************************************************************************************/
#define PDO_MONITOR(pVar, uiVarType, uiLen)                                                          \\
	do                                                                                               \\
	{                                                                                                \\
		if (varAccessGlobVarAdd(GGlobVarHandle, #pVar, uiVarType, uiLen, pVar, VAR_ACC_NAME_ALLOC))  \\
		{                                                                                            \\
			printf("monitor %s type %d len %d error \\n", #pVar, uiVarType, uiLen);                       \\
		}                                                                                            \\
	} while (0)
`;
	const canHfileMonitorHeader = `
/*********************************************************************************************************
  PDO 变量监控初始化宏
*********************************************************************************************************/
#define CAN_PDO_AUTO_MONITOR(pAppName)                     			\\
	do                                                     			\\
	{                                                     		    \\
		RTS_RESULT result;                                 			\\
		GGlobVarHandle = varAccessInit(pAppName, &result); 			\\
		if (RTS_INVALID_HANDLE == GGlobVarHandle)          			\\
		{                                                  			\\
			printf("varAccessInit %s App error\\n", pAppName);       \\
			break;                                         			\\
		}                                                  			\\
`;

	const canHfileMonitorHeaderNone = `
/*********************************************************************************************************
  PDO 变量监控初始化宏
*********************************************************************************************************/
#define CAN_PDO_AUTO_MONITOR(pAppName)
`;
	const canHfileEndWile = `\t} while (0)`;

	const canHfilePdoMapHeader = `
/*********************************************************************************************************
  PDO 变量映射初始化宏
*********************************************************************************************************/
#define CAN_PDO_AUTO_MAP()                                                                    \\
	do                                                                                        \\
	{                                                                                         \\
`;
	const canHfilePdoMapHeaderNone = `
/*********************************************************************************************************
  PDO 变量映射初始化宏
*********************************************************************************************************/
#define CAN_PDO_AUTO_MAP()
`;
	const canHfileVarHeader = `
/*********************************************************************************************************
  PDO 变量声明初始化宏
  GB0M0S2R0 说明 B0 代表busNum, M0 代表masterNum, S2 代表slave CobId, R0 代表rxPdo索引, T0 代表txPdo索引
*********************************************************************************************************/
#define CAN_PDO_AUTO_INIT                    \\
static RTS_HANDLE GGlobVarHandle = NULL; \\
`;
	const canHfileVarHeaderNone = `
/*********************************************************************************************************
  PDO 变量声明初始化宏
*********************************************************************************************************/
#define CAN_PDO_AUTO_INIT					\\
static RTS_HANDLE GGlobVarHandle = NULL;
`;
	const canHfileEnd = `

#ifdef __cplusplus
}
#endif

#endif //__CANOPEN_AUTOGEN_H__

`;
	// 写入文件头及函数声明
	try {
		fs.writeFileSync(canBusOutConfigHFile, canHfileHead, "utf8");
	} catch (err) {
		console.error("写入文件时出现错误 :", err);
		return;
	}

	const patch = ide.pathJoin(projectPath, "taotechplcide.json");
	const jsonData: string = fs.readFileSync(patch, "utf-8");
	const jsonObject = JSON.parse(jsonData) as JsonObject;
	const pdoMap = [] as string[];
	const pdoMonitor = [] as string[];
	const pdoName = [] as string[];

	if (jsonObject.children) {
		for (let i = 0; i < jsonObject.children.length; i++) {
			if (jsonObject.children[i].type === PlcItem.name && jsonObject.children[i].busNum > 0) {
				const bus = jsonObject.children[i].children;
				if (bus) {
					for (let b = 0; b < bus.length; b++) {
						if (bus[b].type === BusManagerItem.name) {
							if (bus[b].canMasterNum > 0) {
								const canMaster = bus[b].children!;
								for (let m = 0; m < canMaster.length; m++) {
									if (
										canMaster[m].type === CanMasterManagerItem.name &&
										canMaster[m].canSlaveNum > 0
									) {
										const canSlave = canMaster[m].children!;
										for (let s = 0; s < canSlave.length; s++) {
											if (canSlave[s].type === CanSlaveManagerItem.name) {
												const [
													rPdoNameDefine,
													rPdoMapDefine,
													rPdoMonitorDefine,
												] = pdoConfig(
													canSlave[s].rxPdo,
													"R",
													bus[b].index,
													m,
													parseInt(canSlave[s].nodeId),
												);
												const [
													tPdoNameDefine,
													tPdoMapDefine,
													tPdoMonitorDefine,
												] = pdoConfig(
													canSlave[s].txPdo,
													"T",
													bus[b].index,
													m,
													parseInt(canSlave[s].nodeId),
												);
												pdoMap.push(...rPdoMapDefine, ...tPdoMapDefine);
												pdoMonitor.push(
													...rPdoMonitorDefine,
													...tPdoMonitorDefine,
												);
												pdoName.push(...rPdoNameDefine, ...tPdoNameDefine);
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	//PDO 变量声明宏生成
	if (pdoName.length) {
		//PDO 变量声明宏生成 头
		try {
			fs.appendFileSync(canBusOutConfigHFile, canHfileVarHeader, "utf8");
		} catch (err) {
			console.error("写入PDO 变量声明宏生成 头 Error :", err);
			return;
		}
		//PDO 变量声明宏生成 变量
		for (let i = 0; i < pdoName.length; i++) {
			try {
				if (i < pdoName.length - 1) {
					fs.appendFileSync(canBusOutConfigHFile, `${pdoName[i]}\t\t\\\n`, "utf8");
				} else {
					fs.appendFileSync(canBusOutConfigHFile, `${pdoName[i]}\n`, "utf8");
				}
			} catch (err) {
				console.error("写入PDO 变量声明宏生成 变量 Error :", err);
				return;
			}
		}
		//PDO 变量声明宏生成 尾
		try {
			fs.appendFileSync(canBusOutConfigHFile, `\n\n`, "utf8");
		} catch (err) {
			console.error("写入PDO 变量声明宏生成 尾 Error :", err);
			return;
		}
	} else {
		// 没有添加can从站时宏定义为空
		try {
			fs.appendFileSync(canBusOutConfigHFile, `${canHfileVarHeaderNone}\n\n`, "utf8");
		} catch (err) {
			console.error("写入PDO 变量声明宏生成 头 Error :", err);
			return;
		}
	}

	// PDO 变量映射宏生成
	if (pdoMap.length) {
		// PDO 变量映射宏生成 头
		try {
			fs.appendFileSync(canBusOutConfigHFile, canHfilePdoMapHeader, "utf8");
		} catch (err) {
			console.error("写入PDO 变量映射宏生成 头 Error :", err);
			return;
		}

		// PDO 变量映射宏生成 变量
		for (let i = 0; i < pdoMap.length; i++) {
			try {
				fs.appendFileSync(canBusOutConfigHFile, `${pdoMap[i]}\n`, "utf8");
			} catch (err) {
				console.error("写入PDO 变量映射宏生成 变量 Error :", err);
				return;
			}
		}

		// PDO 变量映射宏生成 尾
		try {
			fs.appendFileSync(canBusOutConfigHFile, `${canHfileEndWile}\n\n`, "utf8");
		} catch (err) {
			console.error("写入PDO 变量映射宏生成 尾 Error :", err);
			return;
		}
	} else {
		// 没有添加can从站时宏定义为空
		try {
			fs.appendFileSync(canBusOutConfigHFile, `${canHfilePdoMapHeaderNone}\n\n`, "utf8");
		} catch (err) {
			console.error("写入PDO 变量声明宏生成 头 Error :", err);
			return;
		}
	}

	// PDO 变量监控宏生成
	if (pdoMonitor.length) {
		// PDO 变量监控宏生成 头
		try {
			fs.appendFileSync(canBusOutConfigHFile, canHfileMonitorHeader, "utf8");
		} catch (err) {
			console.error("写入PDO 变量监控宏生成头 Error :", err);
			return;
		}

		// PDO 变量监控宏生成 变量
		for (let i = 0; i < pdoMonitor.length; i++) {
			try {
				fs.appendFileSync(canBusOutConfigHFile, `${pdoMonitor[i]}\n`, "utf8");
			} catch (err) {
				console.error("写入PDO 变量监控宏生成 变量 Error :", err);
				return;
			}
		}

		// PDO 变量监控宏生成 尾
		try {
			fs.appendFileSync(canBusOutConfigHFile, `${canHfileEndWile}\n\n`, "utf8");
		} catch (err) {
			console.error("写入PDO 变量监控宏生成 尾 Error :", err);
			return;
		}
	} else {
		// 没有添加can从站时宏定义为空
		try {
			fs.appendFileSync(canBusOutConfigHFile, `${canHfileMonitorHeaderNone}\n\n`, "utf8");
		} catch (err) {
			console.error("写入PDO 变量声明宏生成 头 Error :", err);
			return;
		}
	}

	try {
		fs.appendFileSync(canBusOutConfigHFile, canHfileEnd, "utf8");
	} catch (err) {
		console.error("写入 文件 尾 Error :", err);
		return;
	}
}

export function canBusConfigFileSave() {
	// 应用筛选规则并合并数据
	const patch = ide.pathJoin(projectPath, "taotechplcide.json");
	const canBusOutConfig = ide.pathJoin(projectPath, "canBusConfig.json");

	const jsonData: string = fs.readFileSync(patch, "utf-8");

	const jsonObject = JSON.parse(jsonData) as JsonObject;
	const busData: {
		name: string;
		canPort: string;
		baudrate: string;
		priority: number;
		sdoFormat: string;
		index: number;
		canType: number;
		rackNo: number;
		nodeId: number;
		CANOpenManager: unknown;
	}[] = [];

	// 头文件自动生成
	canBusConfigFileGenerate();

	let busNum = 0;
	let canMaster = [] as unknown[];
	if (jsonObject.children) {
		for (let i = 0; i < jsonObject.children.length; i++) {
			if (jsonObject.children[i].type === PlcItem.name && jsonObject.children[i].busNum > 0) {
				const bus = jsonObject.children[i].children;
				if (bus) {
					for (let j = 0; j < bus.length; j++) {
						if (bus[j].type === BusManagerItem.name) {
							if (bus[j].canMasterNum > 0) {
								canMaster = materDecode(bus[j].children!);
							}

							busData[busNum++] = {
								name: "bus",
								canPort: `can${bus[j].canPort}`,
								baudrate: `${bus[j].baudrate}K`,
								priority: canopenPriority,
								sdoFormat: "standard",
								index: bus[j].index,
								canType: bus[j].canType,
								rackNo: bus[j].rackNo,
								nodeId: bus[j].nodeId,
								CANOpenManager: canMaster as unknown,
							};
						}
					}
				}
			}
		}
	}

	if (busData.length <= 0) {
		return;
	}

	// 然后将合并后的数据保存到文件
	fs.writeFile(
		canBusOutConfig,
		JSON.stringify(busData, null, 2),
		"utf8",
		(err: Error | null | undefined) => {
			if (err) {
				console.error("写入文件时出现错误:", err);
				return;
			}
		},
	);
}
