import vscode from "vscode";

import fs from "fs";
import { finished } from "stream/promises";
import csv from "csv";
import csvSync from "csv/sync";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";
import PlcItem from "./plc-item";
import { JsonRpcNotify } from "@taotech/libdual";
import {
	GetStorePathReq,
	GetStorePathOk,
	GetStorePathErr,
	StoreVariableNotify,
	LoadVariablesReq,
	LoadVariablesRsp,
	Publish,
	SaveTraceCfgNotify,
} from "@taotech/plc-ide-ui";

export default class PlcTraceUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(
		public plcItem: PlcItem,
		public task?: string,
		...disposes: Disposes
	) {
		super(...disposes);
		this.panel = vscode.window.createWebviewPanel(
			"plcTrace",
			"PLC Trace",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		this.willDispose(this.panel);

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(msg: JsonRpcNotify<unknown>) => {
				switch (msg.method) {
					case GetStorePathReq.METHOD:
						void this.rspGetStorePath(msg as GetStorePathReq);
						break;
					case StoreVariableNotify.METHOD:
						this.storeVariable((msg as StoreVariableNotify).params!);
						break;
					case LoadVariablesReq.METHOD:
						void this.loadVariables(msg as LoadVariablesReq);
						break;
					case SaveTraceCfgNotify.METHOD:
						this.plcItem.trace = (msg as SaveTraceCfgNotify).params;
						this.plcItem.parent.save();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private async rspGetStorePath(req: GetStorePathReq) {
		const uri = await vscode.window.showSaveDialog();
		if (uri) {
			await this.panel.webview.postMessage(new GetStorePathOk(req, uri.fsPath));
		} else {
			await this.panel.webview.postMessage(new GetStorePathErr(req, "no folder picked"));
		}
	}

	private storeVariable(publish: Publish) {
		fs.writeFileSync(
			this.plcItem.trace!.store,
			csvSync.stringify(
				publish.map((variable) => [
					variable.name,
					variable.value,
					variable.timestamp,
					variable.task,
				]),
			),
			{ flag: "a" },
		);
	}

	private async loadVariables(req: LoadVariablesReq) {
		const uri = await vscode.window.showOpenDialog({
			canSelectFolders: false,
			canSelectFiles: true,
			canSelectMany: false,
		});
		if (uri && uri[0]) {
			let file = uri[0].fsPath;
			const parser = fs.createReadStream(file).pipe(csv.parse());
			let first = true;
			parser.on("readable", () => {
				let publish: Publish = [];
				let record: string[];
				while ((record = parser.read() as string[]) !== null) {
					publish.push({
						name: record[0],
						value: record[1],
						timestamp: Number(record[2]),
						task: record[3],
					});
					if (publish.length === 100) {
						this.panel.webview.postMessage(
							new LoadVariablesRsp(req, { file, publish }),
						);
						file = "";
						publish = [];
					}
				}
				if (publish.length) {
					this.panel.webview.postMessage(new LoadVariablesRsp(req, { file, publish }));
					file = "";
				}
			});
			await finished(parser);
		}
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-trace-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-trace-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div
						id="root"
						${`data-cfg='${JSON.stringify(this.plcItem.trace)}'`}
						data-ip="${this.plcItem.ip}"
						data-token="${this.plcItem.token}"
						${this.task ? `data-task='${this.task}'` : ""}
					></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
