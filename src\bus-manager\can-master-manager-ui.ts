import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import BusManagerItem from "./bus-manager-item";
import CanMasterManagerItem, { CanMasterManagerCfg } from "./can-master-manager-item";
import { canBusConfigFileSave } from "./can-slave-manager-ui";

//can主设备设置交互消息类型定义
export interface CanMasterMessage {
	command: string;
	data: CanMasterManagerCfg;
}

export interface canMasterMessageEvent {
	data: CanMasterMessage;
}

export default class CanMasterManagerUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;
	busManagerItem: BusManagerItem;
	canMasterManagerItem: CanMasterManagerItem;
	constructor(
		canMasterManagerItem: CanMasterManagerItem,
		busManagerItem: BusManagerItem,
		...disposes: Disposes
	) {
		super(...disposes);

		this.busManagerItem = busManagerItem;
		this.canMasterManagerItem = canMasterManagerItem;

		this.panel = vscode.window.createWebviewPanel(
			"CANMASTER",
			"CAN主站设置",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);

		this.willDispose(this.panel);

		const message: CanMasterMessage = {
			command: "CanMasterMessage",
			data: { ...this.canMasterManagerItem },
		};
		void this.panel.webview.postMessage(message);

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(message: CanMasterMessage) => {
				switch (message.command) {
					case "CanMasterMessage":
						this.canMasterManagerItem.guardingCollapse = message.data.guardingCollapse!;
						this.canMasterManagerItem.syncCollapse = message.data.syncCollapse!;
						this.canMasterManagerItem.timestampCollapse =
							message.data.timestampCollapse!;
						this.canMasterManagerItem.nodeId = message.data.nodeId!;
						this.canMasterManagerItem.autoStartManger = message.data.autoStartManger!;
						this.canMasterManagerItem.autoStartSlave = message.data.autoStartSlave!;
						this.canMasterManagerItem.slaveEnablePolling =
							message.data.slaveEnablePolling!;
						this.canMasterManagerItem.nMTErrorBehavior = message.data.nMTErrorBehavior!;
						this.canMasterManagerItem.guarding.nodeId = message.data.guarding!.nodeId;
						this.canMasterManagerItem.guarding.producerTime =
							message.data.guarding!.producerTime;
						this.canMasterManagerItem.guarding.enableHeartbeat =
							message.data.guarding!.enableHeartbeat;
						this.canMasterManagerItem.sync.enableSyncproduce =
							message.data.sync!.enableSyncproduce;
						this.canMasterManagerItem.sync.cobId = message.data.sync!.cobId;
						this.canMasterManagerItem.sync.cyclePeriod = message.data.sync!.cyclePeriod;
						this.canMasterManagerItem.sync.windowLength =
							message.data.sync!.windowLength;
						this.canMasterManagerItem.sync.enableSyncConsume =
							message.data.sync!.enableSyncConsume;
						this.canMasterManagerItem.timestamp.enableTime =
							message.data.timestamp!.enableTime;
						this.canMasterManagerItem.timestamp.cobId = message.data.timestamp!.cobId;
						this.canMasterManagerItem.timestamp.producerTime =
							message.data.timestamp!.producerTime;
						this.canMasterManagerItem.parent.parent.parent.save();
						canBusConfigFileSave();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidChangeViewState(
			() => {
				if (this.panel?.visible) this.render();

				const message: CanMasterMessage = {
					command: "CanMasterMessage",
					data: { ...this.canMasterManagerItem },
				};
				void this.panel.webview.postMessage(message);
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"plc-ide-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div id="root"></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/can-master-manager-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
