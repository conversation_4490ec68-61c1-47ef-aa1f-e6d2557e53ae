import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { vitePluginForArco } from "@arco-plugins/vite-react";
// https://vitejs.dev/config/
export default defineConfig({
	plugins: [react(), vitePluginForArco()],
	build: {
		outDir: "../../dist/bus-ui/device-scan-ui",
		emptyOutDir: true,
		rollupOptions: {
			output: {
				entryFileNames: "assets/[name].js",
				chunkFileNames: "assets/[name].js",
				assetFileNames: "assets/[name][extname]",
			},
		},
	},
});
