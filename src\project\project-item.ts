import vscode from "vscode";

import Dgram from "dgram";
import Os from "os";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem, { PlcCfg } from "../plc/plc-item";
import TaskSrcItem, { TaskSrcCfg } from "../task/task-src-item";
import { ProjectUpsertParams } from "@taotech/plc-ide-ui";
import { upsertTaskSrc, removeTaskSrc } from "../task/task-src";
import { upsertProjectSrcTasks } from "./project-src";
import TaskItem from "../task/task-item";
import { JsonRpcRequest } from "@taotech/libdual";

export class ProjectCfg implements ProjectUpsertParams {
	type?: string;
	folder!: string;
	languages!: ProjectUpsertParams["languages"];
	toolchain!: ProjectUpsertParams["toolchain"];
	children?: VscodeTreeItem[];
}
export const projectCfgKeys = Object.keys(new ProjectCfg()) as Array<keyof ProjectCfg>;

export default class ProjectItem extends VscodeTreeItem implements ProjectCfg {
	iconPath = new vscode.ThemeIcon("project");
	contextValue = "tao-project";
	declare id: string;

	languages!: ProjectCfg["languages"];
	toolchain!: ProjectCfg["toolchain"];

	folder!: string;
	private sockets: Dgram.Socket[] = [];
	private scanTimerId?: NodeJS.Timeout;

	constructor(public workspaceFolder: vscode.WorkspaceFolder) {
		super(ProjectItem.name, workspaceFolder.name);
		this.id = workspaceFolder.uri.fsPath;
		ide.solution.children.push(this);
		this.disposer.willDispose(() => this.cancelScanPlc());

		const cfg = ide.readJsonFile<ProjectCfg>(
			vscode.Uri.joinPath(workspaceFolder.uri, ide.projectFileName).fsPath,
		)!;
		cfg.folder = workspaceFolder.uri.fsPath;
		projectCfgKeys.forEach((key) => {
			if (key !== "children") (this[key] as unknown) = cfg[key];
			else {
				cfg.children?.forEach((child) => {
					if (child.type === PlcItem.name) {
						new PlcItem(child as unknown as PlcCfg, this);
					}
					if (child.type === TaskSrcItem.name) {
						new TaskSrcItem(child as unknown as TaskSrcCfg, this);
					}
				});
			}
		});
	}

	toJSON(): unknown {
		const cfg: ProjectCfg = new ProjectCfg();
		projectCfgKeys.forEach((key) => ((cfg[key] as unknown) = this[key]));
		return cfg;
	}

	save() {
		ide.writeJsonFile(ide.pathJoin(this.folder, ide.projectFileName), this);
	}

	async inputPlc() {
		const ip = await vscode.window.showInputBox({
			placeHolder: "Enter the IP address here in the format xxx.xxx.xxx.xxx",
			validateInput: (value: string) => {
				if (
					!/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(
						value,
					)
				)
					return "Invalid format";
				else if (
					this.children.some((child) => {
						const plc = child as PlcItem;
						return plc.type === PlcItem.name && plc.ip === value;
					})
				)
					return "Exist already";
				else return undefined;
			},
		});
		if (ip) {
			new PlcItem({ ip }, this);
			ide.solution.refresh(this);
			this.save();
		}
	}

	upsertPlc(ip: string, id: string) {
		if (!id) {
			ide.log.error("upsertPlc: no id");
			return;
		}

		const sameIpPlcs: PlcItem[] = [];
		const sameIdPlcs: PlcItem[] = [];
		for (const child of this.children) {
			if (child.type !== PlcItem.name) continue;
			const plc = child as PlcItem;
			if (plc.ip === ip) sameIpPlcs.push(plc);
			if (plc.id === id) sameIdPlcs.push(plc);
		}

		if (sameIdPlcs.length > 1) throw new Error("multiple plc with same id");
		else if (sameIdPlcs.length === 1) {
			const plc = sameIdPlcs[0];
			if (plc.ip !== ip && plc.contextValue === "tao-plc-off") {
				if (plc.label === plc.ip) plc.label = ip;
				plc.ip = ip;
				ide.solution.refresh(plc);
				this.save();
			}
			return plc;
		} else {
			if (sameIpPlcs.length > 1) throw new Error("multiple plc with same ip");
			else if (sameIpPlcs.length === 1) {
				const plc = sameIpPlcs[0];
				plc.id = id;
				ide.solution.refresh(plc);
				this.save();
				return plc;
			} else {
				new PlcItem({ ip, id }, this);
				ide.solution.refresh(this);
				this.save();
			}
		}
	}

	async scanPlc() {
		this.cancelScanPlc();

		this.scanTimerId = setInterval(() => {
			for (const addrEnties of Object.values(Os.networkInterfaces())) {
				if (!addrEnties) continue;
				for (const addrEntry of addrEnties) {
					if (
						!addrEntry ||
						addrEntry.family !== "IPv4" ||
						addrEntry.internal ||
						!addrEntry.address ||
						!addrEntry.netmask
					)
						continue;

					const socket = Dgram.createSocket("udp4");
					this.sockets.push(socket);

					socket.on("message", (buf, remote) => {
						const msg = JSON.parse(buf.toString()) as { result: { id: string } };
						this.upsertPlc(remote.address, msg.result.id);
					});

					socket.on("error", (error) => {
						this.cancelScanPlc();
						ide.log.error(`scan plc error:${error}`);
					});

					socket.on("listening", () => {
						socket.setBroadcast(true);
						const msg = new JsonRpcRequest("deviceDiscover");
						socket.send(JSON.stringify(msg), 39827, "***************");
					});

					socket.bind({
						address: addrEntry.address,
					});
				}
			}
		}, 2000);

		await vscode.window.withProgress(
			{
				location: vscode.ProgressLocation.Notification,
				title: "Scanning devices......",
				cancellable: true,
			},
			async (progress, token) => {
				token.onCancellationRequested(
					() => {
						this.cancelScanPlc();
					},
					null,
					this.disposer.disposables,
				);

				await new Promise(() => {});
			},
		);
	}

	cancelScanPlc() {
		if (this.scanTimerId !== undefined) {
			clearInterval(this.scanTimerId);
			this.scanTimerId = undefined;
		}
		for (const socket of this.sockets) {
			socket.removeAllListeners();
			socket.close();
		}
		this.sockets = [];
	}

	removeChild(parent: VscodeTreeItem, child: VscodeTreeItem) {
		parent.children.splice(parent.children.indexOf(child), 1);
		child.dispose();
		ide.solution.refresh(parent);
		this.save();
	}

	async newTaskSrc() {
		const label = await vscode.window.showInputBox({
			placeHolder: "Enter the task name",
			validateInput: (value) => {
				if (
					this.children.some(
						(child) => child instanceof TaskSrcItem && child.label === value,
					) ||
					["test", "all", "help"]
						.map((target) => target.toUpperCase())
						.includes(value.toUpperCase())
				)
					return "Illegal task name";
			},
		});
		if (!label) return;
		const cfg = { label };
		upsertTaskSrc(this, cfg, false);
		new TaskSrcItem(cfg, this);
		upsertProjectSrcTasks(this);
		ide.solution.refresh(this);
		this.save();
	}

	async removeTaskSrc(tasksrc: TaskSrcItem) {
		for (const plc of this.children) {
			if (plc instanceof PlcItem) {
				let i = plc.children.length;
				while (i--) {
					const task = plc.children[i];
					if (task instanceof TaskItem && task.label === tasksrc.label) {
						await task.remove();
					}
				}
			}
		}
		this.children.splice(this.children.indexOf(tasksrc), 1);
		removeTaskSrc(ide.pathJoin(this.folder, tasksrc.label));
		upsertProjectSrcTasks(this);
		tasksrc.dispose();
		ide.solution.refresh(this);
		this.save();
	}
}
