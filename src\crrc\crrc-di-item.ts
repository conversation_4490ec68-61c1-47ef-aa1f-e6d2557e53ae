import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcDIUi from "./crrc-di-ui";
import { type CrrcRacksDIsParams, CrrcDIParams } from "@taotech/plcide-ui";
import CrrcRackItem from "./crrc-rack-item";

export default class CrrcDIItem extends VscodeTreeItem {
	declare parent: CrrcRackItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-di";
	command = {
		title: "Property",
		command: "taotech.crrc.di.edit",
		arguments: [this as unknown],
	};

	crrcDIUi?: CrrcDIUi;

	constructor(
		public cfg: CrrcDIParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "DI " + cfg.index, parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcDIUi) this.crrcDIUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcDIUi) {
			this.toJSON();
			this.crrcDIUi = new CrrcDIUi(
				this.parent.parent.ip,
				this.parent.parent.token!,
				this.parent.getChildFreeIndexies(this.cfg.index),
				[{ index: this.parent.cfg.index, children: [this.cfg] }],
				(params) => this.edit(params),
				() => (this.crrcDIUi = undefined),
			);
		} else this.crrcDIUi.reveal();
	}

	edit(params: CrrcRacksDIsParams) {
		const cfg = params[0].children[0];
		if (!this.parent.isChildIndexValidForEdit(cfg.index, this.cfg.index)) return;
		this.cfg = cfg;
		this.label = "DI " + this.cfg.index;
		ide.solution.refresh(this);
		this.parent.parent.parent.save();
	}

	remove() {
		this.parent.parent.parent.removeChild(this.parent, this);
	}
}
