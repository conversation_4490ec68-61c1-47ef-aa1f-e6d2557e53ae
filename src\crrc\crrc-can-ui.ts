import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";
import { CrrcRacksCANsParams, CrrcUpsertCANReq } from "@taotech/plc-ide-ui";
import { JsonRpcNotify } from "@taotech/libdual";

export default class CrrcCanUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(
		public ip: string,
		public token: string,
		private indexies: number[],
		private params: CrrcRacksCANsParams,
		private onSucess: (params: CrrcRacksCANsParams) => void,
		...disposes: Disposes
	) {
		super(...disposes);

		this.panel = vscode.window.createWebviewPanel(
			"CrrcCanProperty",
			`RBM  ${params[0].index} CAN ${params[0].children[0].index} 属性`,
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);

		void this.panel.webview.postMessage({ command: "InitData", params: this.params });

		this.willDispose(this.panel);

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(msg: JsonRpcNotify) => {
				switch (msg.method) {
					case CrrcUpsertCANReq.METHOD:
						const req = msg as unknown as CrrcUpsertCANReq;
						this.panel.title = `RBM  ${params[0].index} CAN ${req.params[0].children[0].index} 属性`;
						this.onSucess(req.params);
						break;
					case "close":
						this.dispose();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "assets", "index.css"]);
		const scriptUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "assets", "index.js"]);
		const productUri = ide.getWebviewUri(webview, ["dist", "plc-ide-ui", "G1-2CAN.png"]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!CANCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div
						id="root"
						data-ip="${this.ip}"
						data-token="${this.token}"
						data-indexies="${JSON.stringify(this.indexies)}"
						${this.params ? `data-params='${JSON.stringify(this.params)}'` : ""}
						data-product-url="${productUri}"
					></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
					<script nonce="${nonce}">
						window.location.hash = "#/crrc-can-ui";
					</script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
