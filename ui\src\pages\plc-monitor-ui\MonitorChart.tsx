import React from 'react';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import type { EChartsReactProps } from 'echarts-for-react/lib/types';
import * as echarts from 'echarts/core';
import type EChartsReact from "echarts-for-react";

import { LineChart } from 'echarts/charts';

import {
    GridComponent,
    TitleComponent,
    TooltipComponent,
    LegendComponent
} from 'echarts/components';

import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
    GridComponent,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    LineChart,
    CanvasRenderer
]);

const MonitorChart = React.forwardRef<EChartsReact, EChartsReactProps>((props, ref) => {
    return (
        <ReactEChartsCore
            ref={ref}
            echarts={echarts}
            {...props}
        />
    );
});

export default MonitorChart;