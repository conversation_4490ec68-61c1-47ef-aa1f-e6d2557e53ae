{"explorer.excludeGitIgnore": false, "search.exclude": {"**/out": true, "**/dist": true, "**/build": true, "**/package-lock.json": true, "setup/windows/compiler": true, "setup/windows/stuff/cmake": true, "setup/windows/stuff/compiler": true}, "files.associations": {"*.css": "tailwindcss"}, "typescript.tsc.autoDetect": "off", "cmake.ignoreCMakeListsMissing": true, "typescript.tsdk": "node_modules\\typescript\\lib", "package-json-upgrade.ignoreVersions": {"@types/node": ">20.16"}, "git.ignoreLimitWarning": true}