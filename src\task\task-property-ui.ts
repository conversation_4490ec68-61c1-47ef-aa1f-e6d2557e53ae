import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import { TaskUpsertNotify, TaskUpsertParams } from "@taotech/plcide-ui";

export default class TaskPropertyUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(
		public ip: string,
		public token: string,
		private modifyTask?: TaskUpsertParams,
		private addTasks?: string[],
		private allTasks?: string[],
		private onSucess?: (params: TaskUpsertParams) => void,
		...disposes: Disposes
	) {
		super(...disposes);
		this.panel = vscode.window.createWebviewPanel(
			"TaskProperty",
			"任务属性",
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		this.willDispose(this.panel, () => (this.onSucess = undefined));

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(msg: TaskUpsertNotify) => {
				if (this.onSucess) this.onSucess(msg.params);
				this.dispose();
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"task-property-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"task-property-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div
						id="root"
						data-ip="${this.ip}"
						data-token="${this.token}"
						${this.modifyTask
							? `data-modify-task='${JSON.stringify(this.modifyTask)}'`
							: ""}
						${this.addTasks && this.addTasks.length
							? `data-add-tasks='${this.addTasks.join()}'`
							: ""}
						${this.allTasks && this.allTasks.length
							? `data-all-tasks='${this.allTasks.join()}'`
							: ""}
					></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
