import vscode from "vscode";

import { ide } from "../extension";
import VscodeTreeItem from "../vscode-tree/vscode-tree-item";
import PlcItem from "../plc/plc-item";
import CrrcDOUi from "./crrc-do-ui";
import { type CrrcRacksDOsParams, CrrcDOParams } from "@taotech/plcide-ui";
import CrrcRackItem from "./crrc-rack-item";

export default class CrrcDOItem extends VscodeTreeItem {
	declare parent: CrrcRackItem;
	iconPath = new vscode.ThemeIcon("server");
	contextValue: string = "tao-crrc-do";
	command = {
		title: "Property",
		command: "taotech.crrc.do.edit",
		arguments: [this as unknown],
	};

	crrcDOUi?: CrrcDOUi;

	constructor(
		public cfg: CrrcDOParams,
		parent: VscodeTreeItem,
	) {
		super(cfg.type, "DO " + cfg.index, parent, true);
		this.disposer.willDispose(() => {
			if (this.crrcDOUi) this.crrcDOUi.dispose();
		});
	}

	toJSON(): unknown {
		return Object.assign(this.cfg, super.toJSON());
	}

	showEdit() {
		if (!this.crrcDOUi) {
			this.toJSON();
			this.crrcDOUi = new CrrcDOUi(
				this.parent.parent.ip,
				this.parent.parent.token!,
				this.parent.getChildFreeIndexies(this.cfg.index),
				[{ index: this.parent.cfg.index, children: [this.cfg] }],
				(params) => this.edit(params),
				() => (this.crrcDOUi = undefined),
			);
		} else this.crrcDOUi.reveal();
	}

	edit(params: CrrcRacksDOsParams) {
		const cfg = params[0].children[0];
		if (!this.parent.isChildIndexValidForEdit(cfg.index, this.cfg.index)) return;
		this.cfg = cfg;
		this.label = "DO " + this.cfg.index;
		ide.solution.refresh(this);
		this.parent.parent.parent.save();
	}

	remove() {
		this.parent.parent.parent.removeChild(this.parent, this);
	}
}
