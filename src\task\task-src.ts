import fs from "fs";

import { ide } from "../extension";
import ProjectItem from "../project/project-item";
import { ProjectUpsertParams, ProjectChildTask } from "@taotech/plcide-ui";
import TaskSrcItem, { TaskSrcCfg } from "./task-src-item";

export function upsertTaskSrc(
	project: ProjectUpsertParams | ProjectItem,
	task: ProjectChildTask,
	update: boolean,
) {
	(task as unknown as TaskSrcCfg).type = TaskSrcItem.name;
	const templatePath = ide.context.asAbsolutePath("template");
	const taskTemplatePath = ide.pathJoin(templatePath, "task");
	const taskPath = ide.pathJoin(project.folder, task.label);

	if (!update) {
		fs.cpSync(taskTemplatePath, taskPath, {
			recursive: true,
			mode: fs.constants.COPYFILE_EXCL,
			force: false,
			errorOnExist: true,
		});
	}

	const taskMethodPatterns = [/(void )(\w+)(Init|Run|Clean)(\(\))/gm];
	const taskMethodReplaces = [`$1${task.label}$3$4`];
	ide.replaceFile(ide.pathJoin(taskPath, "task.h"), taskMethodPatterns, taskMethodReplaces);

	const oldTaskCExt = project.languages.CXX.enabled ? "c" : "cpp";
	const newTaskCExt = project.languages.CXX.enabled ? "cpp" : "c";
	const oldTaskC = ide.pathJoin(taskPath, "task." + oldTaskCExt);
	const newTaskC = ide.pathJoin(taskPath, "task." + newTaskCExt);
	ide.renameSync(oldTaskC, newTaskC);
	ide.replaceFile(newTaskC, taskMethodPatterns, taskMethodReplaces);

	fs.writeFileSync(ide.pathJoin(taskPath, "variables.cmake"), `set(PLC_TASK_NAME ${task.label})`);
}

export function removeTaskSrc(path: string) {
	fs.rmSync(path, { recursive: true });
}
