import vscode from "vscode";

import { ide } from "../extension";
import Disposer, { Disposes } from "../disposer";

import {
	CrrcPlcModbusRtuSlaveParams,
	CrrcUpsertModbusRtuSlaveReq,
} from "@taotech/plcide-ui";
import { JsonRpcNotify } from "@taotech/libdual";

export default class CrrcModbusRtuSlaveUi extends Disposer {
	private readonly panel: vscode.WebviewPanel;

	constructor(
		public ip: string,
		public token: string,
		private params: CrrcPlcModbusRtuSlaveParams,
		private onSucess: (params: CrrcPlcModbusRtuSlaveParams) => void,
		...disposes: Disposes
	) {
		super(...disposes);

		this.panel = vscode.window.createWebviewPanel(
			"CrrcModbusRtuSlaveProperty",
			`Modbus RTU Slave 属性`,
			vscode.ViewColumn.One,
			{ enableScripts: true, enableCommandUris: true, retainContextWhenHidden: true },
		);
		this.willDispose(this.panel);

		this.render();

		this.panel.webview.onDidReceiveMessage(
			(msg: JsonRpcNotify) => {
				switch (msg.method) {
					case CrrcUpsertModbusRtuSlaveReq.METHOD:
						const req = msg as unknown as CrrcUpsertModbusRtuSlaveReq;
						this.onSucess(req.params);
						break;
					case "close":
						this.dispose();
						break;
				}
			},
			null,
			this.disposables,
		);

		this.panel.onDidDispose(() => void this.dispose(), null, this.disposables);
	}

	private render() {
		const webview = this.panel.webview;

		const stylesUri = ide.getWebviewUri(webview, [
			"dist",
			"crrc-modbus-rtu-slave-ui",
			"assets",
			"index.css",
		]);

		const scriptUri = ide.getWebviewUri(webview, [
			"dist",
			"crrc-modbus-rtu-slave-ui",
			"assets",
			"index.js",
		]);

		const nonce = ide.getNonce();

		webview.html = /* HTML */ `
			<!DOCTYPE html>
			<html lang="en">
				<head>
					<meta charset="UTF-8" />
					<meta name="viewport" content="width=device-width, initial-scale=1.0" />
					<meta
						http-equiv="Content-Security-Policy"
						content="default-src ${webview.cspSource}; script-src 'nonce-${nonce}'; connect-src *;"
					/>
					<link rel="stylesheet" type="text/css" href="${stylesUri}" />
				</head>
				<body>
					<div
						id="root"
						data-ip="${this.ip}"
						data-token="${this.token}"
						${this.params ? `data-params='${JSON.stringify(this.params)}'` : ""}
					></div>
					<script type="module" nonce="${nonce}" src="${scriptUri}"></script>
				</body>
			</html>
		`;
	}

	reveal() {
		this.panel.reveal();
	}
}
