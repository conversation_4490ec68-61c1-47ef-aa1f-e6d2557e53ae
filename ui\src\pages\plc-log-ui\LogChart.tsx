import React from 'react';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import type { EChartsReactProps } from 'echarts-for-react/lib/types';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import type EChartsReact from "echarts-for-react";


import {
    <PERSON><PERSON>hart,
    BarChart,
} from 'echarts/charts';

import {
    GridComponent,
    LegendComponent,
} from 'echarts/components';


echarts.use([
    GridComponent,
    LegendComponent,
    LineC<PERSON>,
    Bar<PERSON>hart,
    CanvasRenderer
]);

const LogChart = React.forwardRef<EChartsReact, EChartsReactProps>((props, ref) => {
    return (
        <ReactEChartsCore
            ref={ref}
            echarts={echarts}
            {...props}
        />
    );
});

export default LogChart;