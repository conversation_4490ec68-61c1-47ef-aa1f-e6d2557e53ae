import { JsonRpcRequest } from "@taotech/libdual";

export class CrrcDOChannel {
	constructor(
		public mode: number = 0,
		public cycle: number = 100,
		public frequency: number = 100,
	) {}
}

export class CrrcDOParams {
	constructor(
		public index: number = 0,
		public channel1to4 = new CrrcDOChannel(),
		public channel5to8 = new CrrcDOChannel(),
		public channel9to12 = new CrrcDOChannel(),
		public channel13to16 = new CrrcDOChannel(),
		public type = CrrcDOParams.name,
	) {}
}

export interface CrrcRackDOsParams {
	index: number;
	children: CrrcDOParams[];
}

export interface CrrcRacksDOsParams extends Array<CrrcRackDOsParams> {}

export class CrrcUpsertDOReq extends JsonRpcRequest<CrrcRacksDOsParams> {
	static METHOD = "upsert";
	declare params: CrrcRacksDOsParams;
	constructor(params: CrrcRacksDOsParams, token?: string) {
		super(CrrcUpsertDOReq.METHOD, params, token);
	}
}
