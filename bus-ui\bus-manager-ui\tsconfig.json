{
	"compilerOptions": {
		"module": "ESNext",
		"target": "ES2020",
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"rootDir": "src",
		"sourceMap": true,
		"useDefineForClassFields": true,
		"skipLibCheck": true,

		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true
	},
	"include": ["src"],
	"references": [{ "path": "./tsconfig.node.json" }]
}
